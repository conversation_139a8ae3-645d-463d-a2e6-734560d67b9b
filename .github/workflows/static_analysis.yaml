name: Static analysis
on:
  push:
    branches:
      - develop
      - staging
      - master
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  Black-Formatter-Check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Find black version
        id: black-version
        run: |
          BLACK_VERSION=$(grep -E "^black==" requirements-dev.txt | cut -d'=' -f3)
          echo "BLACK_VERSION=$BLACK_VERSION" >> $GITHUB_OUTPUT
      - uses: psf/black@stable
        with:
          options: ". --check"
          version: ${{ steps.black-version.outputs.BLACK_VERSION }}
  Ruff-Check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Find ruff version
        id: ruff-version
        run: |
          RUFF_VERSION=$(grep -E "^ruff==" requirements-dev.txt | cut -d'=' -f3)
          echo "RUFF_VERSION=$RUFF_VERSION" >> $GITHUB_OUTPUT
      - uses: chartboost/ruff-action@v1
        with:
          version: ${{ steps.ruff-version.outputs.RUFF_VERSION }}
  Pyright-Check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.13"
      - uses: yezz123/setup-uv@v4
      - run: |
          find . \( -name "requirements.txt" -o -name "requirements-dev.txt" \) -exec uv pip install --system -r {} \;
      - name: Find pyright version
        id: pyright-version
        run: |
          PYRIGHT_VERSION=$(grep -E "^pyright==" requirements-dev.txt | cut -d'=' -f3)
          echo "PYRIGHT_VERSION=$PYRIGHT_VERSION" >> $GITHUB_OUTPUT
      - uses: jakebailey/pyright-action@v2
        with:
          version: ${{ steps.pyright-version.outputs.PYRIGHT_VERSION }}
          extra-args: |
            services/base
            services/data_service/domain
            services/data_service/application
            services/data_service/api
            services/serverless/apps/data_consistency_validator
            services/serverless/apps/notify_handler
            services/serverless/apps/single_correlation_app
            services/serverless/apps/trend_insights
  Dependabot-Check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: validate-dependabot
        uses: marocchino/validate-dependabot@v3.0.0
