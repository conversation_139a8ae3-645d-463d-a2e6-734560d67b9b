from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.core_event import CoreEventFields
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.events.note import NoteFields
from services.base.domain.schemas.events.plan import PlanFields
from services.base.domain.schemas.events.symptom import SymptomFields
from services.base.domain.schemas.templates.template import TemplateFields
from services.file_service.application.enums.exportable_data_type import ExportableType

V3CSVMappings = {
    ExportableType.CoreEvent: [
        DocumentLabels.TIMESTAMP,
        EventFields.TYPE,
        EventFields.CATEGORY,
        EventFields.NAME,
        DocumentLabels.DURATION,
        # Core Event Fields
        CoreEventFields.RATING,
        CoreEventFields.NOTE,
    ],
    ExportableType.Note: [
        DocumentLabels.TIMESTAMP,
        EventFields.TYPE,
        EventFields.CATEGORY,
        EventFields.NAME,
        # Note Fields
        NoteFields.NOTE,
    ],
    ExportableType.Symptom: [
        DocumentLabels.TIMESTAMP,
        EventFields.TYPE,
        EventFields.CATEGORY,
        EventFields.NAME,
        DocumentLabels.DURATION,
        # Symptom Fields
        SymptomFields.RATING,
        SymptomFields.NOTE,
        SymptomFields.BODY_PARTS,
    ],
}

V3NonEventCSVMappings = {
    ExportableType.EventTemplate: [
        EventFields.TYPE,
        EventFields.NAME,
        TemplateFields.DOCUMENT_NAME,
        TemplateFields.DOCUMENT_TYPE,
    ],
    ExportableType.GroupTemplate: [
        EventFields.TYPE,
        EventFields.NAME,
        TemplateFields.TEMPLATE_IDS,
    ],
    ExportableType.Plan: [
        EventFields.NAME,
        PlanFields.TEMPLATE_ID,
        PlanFields.STREAK,
        PlanFields.IS_URGENT,
        PlanFields.IS_CONFIRMATION_REQUIRED,
        PlanFields.PRIORITY,
        PlanFields.PROMPT,
        PlanFields.CURRENT_COMPLETED,
        PlanFields.NEXT_SCHEDULED_AT,
        PlanFields.FIRST_COMPLETED_AT,
        PlanFields.MAX_COMPLETED,
        PlanFields.NOTE,
    ],
    ExportableType.UseCase: [
        EventFields.NAME,
    ],
}
