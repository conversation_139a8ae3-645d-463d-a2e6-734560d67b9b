from datetime import datetime

import pytest

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.user_service.application.use_cases.plan.plan_stringifier import PlanStringifier


class TestPlanStringifier:
    def test_plural_returns_s_suffix(self):
        assert PlanStringifier.plural(0) == "s"
        assert PlanStringifier.plural(1) == ""
        assert PlanStringifier.plural(PrimitiveTypesGenerator.generate_random_int(min_value=2)) == "s"

    @pytest.mark.parametrize(
        "plan_count, expected_output",
        [
            (0, "You have 0 late plans"),
            (1, "You have 1 late plan"),
            (2, "You have 2 late plans"),
        ],
    )
    def test_stringify_plan_late_title(self, plan_count: int, expected_output: str):
        assert expected_output == PlanStringifier.stringify_plan_late_title(plan_count=plan_count)

    @pytest.mark.parametrize(
        "plan_count, expected_output",
        [
            (0, "Today, you have 0 upcoming plans"),
            (1, "Today, you have 1 upcoming plan"),
            (2, "Today, you have 2 upcoming plans"),
        ],
    )
    def test_stringify_plan_upcoming_title(self, plan_count: int, expected_output: str):
        assert expected_output == PlanStringifier.stringify_plan_upcoming_title(plan_count=plan_count)

    @pytest.mark.parametrize(
        "now, scheduled_at, expected_output",
        [
            (datetime(2023, 1, 31, 15, 0), datetime(2023, 1, 31, 12, 0), "3 hours"),
            (datetime(2023, 1, 31, 15, 0), datetime(2023, 1, 31, 14, 0), "1 hour"),
            (datetime(2023, 2, 2, 12, 0), datetime(2023, 1, 31, 12, 0), "2 days"),
            (datetime(2023, 2, 2, 12, 0), datetime(2023, 2, 1, 12, 0), "1 day"),
        ],
    )
    def test_stringify_plan_lateness_returns_how_late(self, now, scheduled_at, expected_output):
        assert expected_output == PlanStringifier.stringify_plan_lateness(delta=now - scheduled_at)

    @pytest.mark.parametrize(
        "plan_name, now, next_scheduled_at, expected_output",
        [
            ("hello", datetime(2023, 1, 31, 15, 0), datetime(2023, 1, 31, 12, 0), "\u2022\thello is 3 hours late"),
            ("world", datetime(2023, 1, 31, 15, 0), datetime(2023, 1, 31, 14, 0), "\u2022\tworld is 1 hour late"),
            ("hello2", datetime(2023, 2, 2, 12, 0), datetime(2023, 1, 31, 12, 0), "\u2022\thello2 is 2 days late"),
            ("world2", datetime(2023, 2, 2, 12, 0), datetime(2023, 2, 1, 12, 0), "\u2022\tworld2 is 1 day late"),
        ],
    )
    def test_stringify_plan_late_returns_name_and_time(self, plan_name, now, next_scheduled_at, expected_output):
        assert expected_output == PlanStringifier.stringify_plan_late(
            plan_name=plan_name,
            delta=now - next_scheduled_at,
        )

    @pytest.mark.parametrize(
        "plan_name, next_scheduled_at, expected_output",
        [
            ("hello", datetime(2023, 1, 31, 15, 0), "\u2022\thello at 15:00"),
            ("world", datetime(2023, 1, 31, 16, 0), "\u2022\tworld at 16:00"),
        ],
    )
    def test_stringify_plan_upcoming_returns_name_and_time(self, plan_name, next_scheduled_at, expected_output):
        assert expected_output == PlanStringifier.stringify_plan_upcoming(
            plan_name=plan_name, next_scheduled_at=next_scheduled_at
        )
