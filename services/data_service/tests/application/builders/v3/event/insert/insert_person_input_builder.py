from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.person import PersonIdentifier
from services.data_service.application.use_cases.events.models.insert_person_input import InsertPersonInput
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertPersonInputBuilder(InsertEventBuilderBase, PersonIdentifier):
    def __init__(self):
        super().__init__()
        self._contact_id = None

    def with_contact_id(self, contact_id: UUID):
        self._contact_id = contact_id
        return self

    def build(self) -> InsertPersonInput:
        return InsertPersonInput(
            type=DataType.Person,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=PersonRelationship),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=EventInputAssetBuilder().build_n(),
            template_id=self._template_id,
            contact_id=self._contact_id or uuid4(),
        )
