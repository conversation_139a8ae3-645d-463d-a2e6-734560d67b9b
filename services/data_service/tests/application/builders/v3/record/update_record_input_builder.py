from __future__ import annotations

import random
from datetime import datetime
from typing import Self
from uuid import UUID, uuid4

from services.base.tests.domain.builders.builder_base import BuilderBase
from services.data_service.application.use_cases.records.update_record_inputs import UpdateRecordInputs
from services.data_service.type_resolver import TypeResolver


class UpdateRecordInputBuilder(BuilderBase):

    def __init__(self):
        self._id: UUID | None = None
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None
        self._type_id: str | None = None

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def build(self) -> UpdateRecordInputs:
        builder = (
            TypeResolver.get_update_record_input_builder(type_id=self._type_id)
            if self._type_id
            else random.choice(TypeResolver.UPDATE_RECORD_INPUT_BUILDERS)
        )
        return (
            builder()
            .with_id(id=self._id or uuid4())
            .with_timestamp(self._timestamp)
            .with_end_time(self._end_time)
            .build()
        )

    def with_type_id(self, type_id: str) -> Self:
        self._type_id = type_id
        return self
