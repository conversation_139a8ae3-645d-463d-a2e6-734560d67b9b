from datetime import datetime, timedelta
from typing import Sequence
from zoneinfo import ZoneInfo

import pytest

from services.base.domain.schemas.events.document_base import TimestampDocument
from services.data_service.application.use_cases.event_correlation.document_matcher import DocumentMatcher


class TestDocumentMatcher:
    @pytest.mark.parametrize(
        "ts,doc_timestamps,delta,expected_indices",
        [
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                [0, 1],
            ),
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 10, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=2),
                [0],
            ),
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 10, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 10, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(minutes=30),
                [],
            ),
        ],
    )
    def test_find_matching_docs_before(
        self,
        ts: datetime,
        doc_timestamps: Sequence[datetime],
        delta: timedelta,
        expected_indices: Sequence[int],
    ):
        # Arrange
        docs = [TimestampDocument(timestamp=dt) for dt in doc_timestamps]

        # Act
        result = DocumentMatcher.find_matching_docs(ts=ts, docs=docs, temporal_type="before", delta=delta)

        # Assert
        expected_docs = [docs[i] for i in expected_indices]
        assert len(result) == len(expected_docs)
        for doc in result:
            assert doc in expected_docs

    @pytest.mark.parametrize(
        "ts,doc_timestamps,delta,expected_indices",
        [
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 45, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                [1, 2],
            ),
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 13, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=2),
                [2],
            ),
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                [],
            ),
        ],
    )
    def test_find_matching_docs_after(
        self,
        ts: datetime,
        doc_timestamps: Sequence[datetime],
        delta: timedelta,
        expected_indices: Sequence[int],
    ):
        # Arrange
        docs = [TimestampDocument(timestamp=dt) for dt in doc_timestamps]

        # Act
        result = DocumentMatcher.find_matching_docs(ts=ts, docs=docs, temporal_type="after", delta=delta)

        # Assert
        expected_docs = [docs[i] for i in expected_indices]
        assert len(result) == len(expected_docs)
        for doc in result:
            assert doc in expected_docs

    @pytest.mark.parametrize(
        "ts,doc_timestamps,delta,expected_indices",
        [
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 29, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                [1],
            ),
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 30, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 31, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                [0],
            ),
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 10, 0, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 14, 0, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(hours=1),
                [],
            ),
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=ZoneInfo("UTC")),
                [
                    datetime(2023, 1, 1, 11, 45, tzinfo=ZoneInfo("UTC")),
                    datetime(2023, 1, 1, 12, 15, tzinfo=ZoneInfo("UTC")),
                ],
                timedelta(minutes=30),
                [1],
            ),
        ],
    )
    def test_find_matching_docs_closest(
        self,
        ts: datetime,
        doc_timestamps: Sequence[datetime],
        delta: timedelta,
        expected_indices: Sequence[int],
    ):
        # Arrange
        docs = [TimestampDocument(timestamp=dt) for dt in doc_timestamps]

        # Act
        result = DocumentMatcher.find_matching_docs(ts=ts, docs=docs, temporal_type="closest", delta=delta)

        # Assert
        expected_docs = [docs[i] for i in expected_indices]
        assert len(result) == len(expected_docs)
        for doc in result:
            assert doc in expected_docs

    @pytest.mark.parametrize(
        "docs, target_ts, expected_idx",
        [
            # Before all
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 4, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC")),
                0,
            ),
            # After all
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 4, tzinfo=ZoneInfo("UTC")),
                3,
            ),
            # Equal
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC")),
                1,
            ),
            # Between
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 5, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC")),
                1,
            ),
            # Documents with duplicate timestamps
            (
                [
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC"))),
                    TimestampDocument(timestamp=datetime(2023, 1, 3, tzinfo=ZoneInfo("UTC"))),
                ],
                datetime(2023, 1, 2, tzinfo=ZoneInfo("UTC")),
                2,
            ),
        ],
    )
    def test_find_greater_timestamp_passes(
        self, docs: Sequence[TimestampDocument], target_ts: datetime, expected_idx: int
    ):
        # Act
        result = DocumentMatcher._find_greater_timestamp(docs=docs, ts=target_ts)

        # Assert
        assert result == expected_idx

    def test_find_greater_timestamp_empty_docs_raises(self):
        # Arrange
        docs = []
        target_ts = datetime(2023, 1, 1)

        # Act & Assert
        with pytest.raises(ValueError):
            DocumentMatcher._find_greater_timestamp(docs=docs, ts=target_ts)
