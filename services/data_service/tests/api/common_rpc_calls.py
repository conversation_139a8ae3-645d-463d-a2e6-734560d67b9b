from typing import Any

from httpx import ASGITransport, AsyncClient, Response
from starlette import status

from services.base.application.retry import retry_until_value
from services.data_service.main import app


async def _call_get_endpoint(
    request_url: str, headers: dict | None = None, query_params: dict[str, Any] | None = None, retry: bool = True
) -> Response:
    @retry_until_value(
        max_times=2 if retry else 0,
        delay=0.5,
        value_validator=lambda x: x.status_code == status.HTTP_200_OK,
        raise_on_too_many_retries=False,
    )
    async def get():
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as async_client:
            return await async_client.get(request_url, params=query_params, headers=headers)

    return await get()


async def _call_patch_endpoint(
    request_url: str, json: dict | None = None, headers: dict | None = None, retry: bool = True
) -> Response:
    @retry_until_value(
        max_times=2 if retry else 0,
        delay=0.5,
        value_validator=lambda x: x.status_code == status.HTTP_200_OK,
        raise_on_too_many_retries=False,
    )
    async def patch():
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as async_client:
            return await async_client.patch(request_url, headers=headers, json=json)

    return await patch()


async def _call_post_endpoint(
    request_url: str,
    json: dict | None = None,
    query_params: dict[str, Any] | None = None,
    headers: dict | None = None,
    retry: bool = True,
) -> Response:
    @retry_until_value(
        max_times=2 if retry else 0,
        delay=0.5,
        value_validator=lambda x: x.status_code == status.HTTP_200_OK,
        raise_on_too_many_retries=False,
    )
    async def post():
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as async_client:
            return await async_client.post(request_url, headers=headers, json=json, params=query_params)

    return await post()


async def _call_delete_endpoint(
    request_url: str,
    json: dict | None = None,
    query_params: dict[str, Any] | None = None,
    headers: dict | None = None,
    retry: bool = True,
) -> Response:
    @retry_until_value(
        max_times=2 if retry else 0,
        delay=0.5,
        value_validator=lambda x: x.status_code == status.HTTP_200_OK,
        raise_on_too_many_retries=False,
    )
    async def delete():
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as async_client:
            return await async_client.request(
                method="delete", url=request_url, params=query_params, headers=headers, json=json
            )

    return await delete()
