from __future__ import annotations

from services.base.domain.schemas.query.aggregations import AggregationMethod, SimpleAggregationMethod
from services.data_service.api.queries.event_query_api import EventQueryAPI
from services.data_service.application.use_cases.calendar_aggregation.calendar_frequency_distribution_use_case import (
    CalendarAggregationType,
)


class CalendarHistogramAggregationAPIRequestInputBuilder:

    def __init__(self):
        self._query: EventQueryAPI | None = None
        self._calendar_aggregation_type: CalendarAggregationType | None = None
        self._fill_null_values: bool | None = None
        self._field_name: str | None = None
        self._aggregation_method: AggregationMethod | None = None

    def with_query(self, query: EventQ<PERSON>yAPI) -> CalendarHistogramAggregationAPIRequestInputBuilder:
        self._query = query
        return self

    def with_calendar_aggregation_type(
        self, calendar_aggregation_type: CalendarAggregationType
    ) -> CalendarHistogramAggregationAPIRequestInputBuilder:
        self._calendar_aggregation_type = calendar_aggregation_type
        return self

    def with_fill_null_values(self, fill_null_values: bool) -> CalendarHistogramAggregationAPIRequestInputBuilder:
        self._fill_null_values = fill_null_values
        return self

    def with_field_name(self, field: str) -> CalendarHistogramAggregationAPIRequestInputBuilder:
        self._field_name = field
        return self

    def with_aggregation_method(
        self, aggregation_method: SimpleAggregationMethod
    ) -> CalendarHistogramAggregationAPIRequestInputBuilder:
        self._aggregation_method = aggregation_method
        return self

    def build_body_as_dict(self) -> dict:
        query_as_dict = {"queries": self._query.model_dump(mode="json")["queries"]} if self._query else {}

        if self._calendar_aggregation_type:
            query_as_dict["calendar_aggregation_type"] = self._calendar_aggregation_type.value

        if self._field_name:
            query_as_dict["field_name"] = self._field_name

        if self._aggregation_method:
            query_as_dict["aggregation_method"] = self._aggregation_method.value

        if self._fill_null_values is not None:
            query_as_dict["fill_null_values"] = self._fill_null_values

        return query_as_dict
