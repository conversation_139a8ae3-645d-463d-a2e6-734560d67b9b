import json
from asyncio import TaskGroup
from datetime import time, timed<PERSON><PERSON>
from typing import Awaitable, Callable

from starlette import status

from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.assets import Assets
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.input_validators.shared import InputAsset, InputTimeIntervalModel
from services.base.application.object_storage_service import ObjectStorageService
from services.base.application.utils.urls import join_as_url
from services.base.domain.constants.time_constants import SECONDS_IN_HOUR
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.resting_heart_rate import RestingHeartRate
from services.base.domain.schemas.shared import AssetReferenceModel
from services.base.domain.schemas.sleep import Sleep
from services.base.domain.schemas.steps import Steps
from services.data_service.api.output_models.diary_event_api_output import DiaryEventAPIOutput
from services.data_service.api.output_models.heart_rate_api_output import HeartRateAPIOutput
from services.data_service.api.output_models.location_api_output import LocationAPIOutput
from services.data_service.api.output_models.resting_heart_rate_api_output import RestingHeartRateAPIOutput
from services.data_service.api.output_models.sleep_api_output import SleepAPIOutput
from services.data_service.api.output_models.steps_api_output import StepsAPIOutput
from services.data_service.api.request_models.load_event_api_request_input import LoadDiaryEventsAPIRequestInput
from services.data_service.api.request_models.load_heart_rate_api_request_input import LoadHeartRateAPIRequestInput
from services.data_service.api.request_models.load_location_api_request_input import LoadLocationAPIRequestInput
from services.data_service.api.request_models.load_resting_heart_rate_api_request_input import (
    LoadRestingHeartRateAPIRequestInput,
)
from services.data_service.api.request_models.load_sleep_api_request_input import LoadSleepAPIRequestInput
from services.data_service.api.request_models.load_steps_api_request_input import LoadStepsAPIRequestInput
from services.data_service.api.urls import AssetsEndpointUrls, DataCrudEndpointsUrls
from services.data_service.application.builders.load_event_input_builder import LoadDiaryEventsInputBuilder
from services.data_service.application.builders.load_heart_rate_input_builder import LoadHeartRateInputBuilder
from services.data_service.application.builders.load_location_input_builder import LoadLocationInputBuilder
from services.data_service.application.builders.load_resting_heart_rate_input_builder import (
    LoadRestingHeartRateInputBuilder,
)
from services.data_service.application.builders.load_sleep_input_builder import LoadSleepInputBuilder
from services.data_service.application.builders.load_steps_input_builder import LoadStepsInputBuilder
from services.data_service.application.builders.metadata_input_builder import MetadataInputBuilder
from services.data_service.application.use_cases.loading.day_splitter import DaySplitter
from services.data_service.application.use_cases.loading.metadata_input import MetadataInputModel
from services.data_service.application.use_cases.loading.sleep.models.load_sleep_input import LoadSleepInput
from services.data_service.tests.api.common_calls import _delete_user_documents
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint, _call_post_endpoint


class TestLoadingEndpoints:
    @staticmethod
    async def validate_assets_content(
        input_assets: list[InputAsset], output_assets: list[AssetReferenceModel], headers: dict
    ):
        async def validate_asset_content(input_asset: InputAsset, output_asset: AssetReferenceModel):
            request_url = join_as_url(
                base_url=AssetsEndpointUrls.BY_ID, query_params={"asset_id": output_asset.asset_id}
            )
            response = await _call_get_endpoint(request_url=request_url, headers=headers)
            assert response
            assert response.content == input_asset.content

        async with TaskGroup() as group:
            for input_asset, output_asset in zip(input_assets, output_assets):
                group.create_task(coro=validate_asset_content(input_asset=input_asset, output_asset=output_asset))

    async def test_load_diary_event_should_pass(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        depr_event_repository: DeprEventRepository,
        object_storage_service: ObjectStorageService,
    ):
        # Arrange
        input_data = LoadDiaryEventsInputBuilder().build_series(max_time_delta=timedelta(seconds=100))
        request_input = LoadDiaryEventsAPIRequestInput(documents=input_data, metadata=MetadataInputBuilder().build())
        user, headers = await user_headers_factory()

        # Act
        request_url = DataCrudEndpointsUrls.EVENT
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
        )

        # Assert
        assert response
        assert response.status_code == status.HTTP_200_OK, response.content

        payload = CommonDocumentsResponse[DiaryEventAPIOutput](**response.json())
        assert payload
        for input, output in zip(input_data, payload.documents):
            assert output
            assert input.explanation == output.explanation
            assert input.timestamp == output.timestamp
            assert input.tags == output.tags
            assert input.end_time == output.end_time
            assert (input.end_time - input.timestamp).total_seconds() == output.duration
            assert input.type == output.type
            assert input.custom_data == output.custom_data
            assert len(input.assets) == len(output.asset_references)

            await self.validate_assets_content(
                input_assets=input.assets, output_assets=output.asset_references, headers=headers
            )

        # Cleanup
        await _delete_user_documents(
            user_uuid=user.user_uuid, data_schema=DiaryEvents, event_repo=depr_event_repository
        )
        await object_storage_service.delete_container(
            container_name=Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
        )

    @staticmethod
    def _slice_time_interval_data[T: InputTimeIntervalModel](input_data: list[T], time_delta: timedelta):
        start = None
        slice = []
        for data in input_data:
            start = start or data.timestamp
            if any((data.timestamp >= start + time_delta, data.end_time >= start + time_delta)):
                yield slice
                slice = [data]
                start = data.timestamp
                continue
            slice.append(data)
        yield slice

    async def test_load_location_should_pass(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        depr_event_repository: DeprEventRepository,
    ):
        # Arrange
        input_data = LoadLocationInputBuilder().build_series(max_time_delta=timedelta(seconds=SECONDS_IN_HOUR))
        metadata = MetadataInputBuilder().build()
        request_input = LoadLocationAPIRequestInput(documents=input_data, metadata=metadata)
        user, headers = await user_headers_factory()

        # Act
        request_url = DataCrudEndpointsUrls.LOCATION
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response
        assert response.status_code == status.HTTP_200_OK, response.content

        payload = CommonDocumentsResponse[LocationAPIOutput](**response.json())
        assert payload
        output = payload.documents
        start = output[0].timestamp
        end = output[-1].end_time

        for output_result in output:
            assert start <= output_result.timestamp <= end
            assert output_result.duration <= SECONDS_IN_HOUR

        split = [[d] for d in input_data]

        for data_in, data_out in zip(split, output):
            assert all([data_out.timestamp <= r.timestamp <= data_out.end_time for r in data_in])
            assert all(r.end_time <= data_out.end_time for r in data_in)
            assert data_out.timestamp == data_in[0].timestamp
            assert data_out.end_time == data_in[-1].end_time

            assert data_out.start_coordinates.latitude == data_in[0].data.latitude
            assert data_out.start_coordinates.longitude == data_in[0].data.longitude
            assert data_out.end_coordinates.latitude == data_in[-1].data.latitude
            assert data_out.end_coordinates.longitude == data_in[-1].data.longitude

            input_latitudes = [r.data.latitude for r in data_in]
            input_longitudes = [r.data.longitude for r in data_in]
            assert round(sum(input_latitudes) / len(input_latitudes), 7) == data_out.average_coordinates.latitude
            assert round(sum(input_longitudes) / len(input_longitudes), 7) == data_out.average_coordinates.longitude

            assert data_out.start_altitude == data_in[0].data.altitude
            assert data_out.end_altitude == data_in[-1].data.altitude

            input_altitudes = [r.data.altitude for r in data_in]
            if any(input_altitudes):
                assert round(sum(input_altitudes) / len(input_altitudes), 3) == data_out.average_altitude

        # Cleanup
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=Location, event_repo=depr_event_repository)

    async def test_load_heart_rate_should_pass(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        depr_event_repository: DeprEventRepository,
    ):
        # Arrange
        input_data = LoadHeartRateInputBuilder().build_series(max_time_delta=timedelta(seconds=SECONDS_IN_HOUR))
        metadata = MetadataInputBuilder().build()
        request_input = LoadHeartRateAPIRequestInput(documents=input_data, metadata=metadata)
        user, headers = await user_headers_factory()

        # Act
        request_url = DataCrudEndpointsUrls.HEART_RATE
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
        )

        # Assert
        assert response
        assert response.status_code == status.HTTP_200_OK, response.content

        payload = CommonDocumentsResponse[HeartRateAPIOutput](**response.json())
        assert payload
        output = payload.documents
        start = output[0].timestamp
        end = output[-1].end_time

        for output_result in output:
            assert start <= output_result.timestamp <= end
            assert output_result.duration <= SECONDS_IN_HOUR

        split = list(
            self._slice_time_interval_data(input_data=input_data, time_delta=timedelta(seconds=SECONDS_IN_HOUR))
        )

        for data_in, data_out in zip(split, output):
            assert all(data_out.timestamp <= r.timestamp <= data_out.end_time for r in data_in)
            assert all(r.end_time <= data_out.end_time for r in data_in)
            assert data_out.timestamp == data_in[0].timestamp
            assert data_out.end_time == data_in[-1].end_time

            input_values = [r.value for r in data_in]
            assert max(input_values) == data_out.bpm_max
            assert min(input_values) == data_out.bpm_min
            assert round(sum(input_values) / len(input_values), 2) == data_out.bpm_avg

        # Cleanup
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=HeartRate, event_repo=depr_event_repository)

    async def test_load_resting_heart_rate_should_pass(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        depr_event_repository: DeprEventRepository,
    ):
        # Arrange
        input_data = LoadRestingHeartRateInputBuilder().build_series(max_time_delta=timedelta(seconds=SECONDS_IN_HOUR))
        metadata = MetadataInputBuilder().build()
        request_input = LoadRestingHeartRateAPIRequestInput(documents=input_data, metadata=metadata)
        user, headers = await user_headers_factory()

        # Act
        request_url = DataCrudEndpointsUrls.RESTING_HEART_RATE
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
        )

        # Assert
        assert response
        assert response.status_code == status.HTTP_200_OK, response.content

        payload = CommonDocumentsResponse[RestingHeartRateAPIOutput](**response.json())
        assert payload
        output = payload.documents
        start = output[0].timestamp
        end = output[-1].end_time

        for output_document in output:
            assert start <= output_document.timestamp <= end
            assert output_document.duration <= SECONDS_IN_HOUR

        split = list(
            self._slice_time_interval_data(input_data=input_data, time_delta=timedelta(seconds=SECONDS_IN_HOUR))
        )

        for data_in, data_out in zip(split, output):
            assert all(data_out.timestamp <= r.timestamp <= data_out.end_time for r in data_in)
            assert all(r.end_time <= data_out.end_time for r in data_in)
            assert data_out.timestamp == data_in[0].timestamp
            assert data_out.end_time == data_in[-1].end_time

            input_values = [r.value for r in data_in]
            assert max(input_values) == data_out.bpm_max
            assert min(input_values) == data_out.bpm_min
            assert round(sum(input_values) / len(input_values), 2) == data_out.bpm_avg

        # Cleanup
        await _delete_user_documents(
            user_uuid=user.user_uuid, data_schema=RestingHeartRate, event_repo=depr_event_repository
        )

    async def test_load_steps_should_pass(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        depr_event_repository: DeprEventRepository,
    ):
        # Arrange
        input_data = LoadStepsInputBuilder().build_series(max_time_delta=timedelta(seconds=SECONDS_IN_HOUR))
        metadata = MetadataInputBuilder().build()
        request_input = LoadStepsAPIRequestInput(documents=input_data, metadata=metadata)
        user, headers = await user_headers_factory()

        # Act
        request_url = DataCrudEndpointsUrls.STEPS
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
        )

        # Assert
        assert response
        assert response.status_code == status.HTTP_200_OK, response.content

        payload = CommonDocumentsResponse[StepsAPIOutput](**response.json())
        assert payload
        output = payload.documents
        start = output[0].timestamp
        end = output[-1].end_time

        for output_result in output:
            assert start <= output_result.timestamp <= end
            assert output_result.duration <= SECONDS_IN_HOUR

        split = list(
            self._slice_time_interval_data(input_data=input_data, time_delta=timedelta(seconds=SECONDS_IN_HOUR))
        )

        for data_in, data_out in zip(split, output):
            assert all(data_out.timestamp <= r.timestamp <= data_out.end_time for r in data_in)
            assert all(r.end_time <= data_out.end_time for r in data_in)
            assert data_out.timestamp == data_in[0].timestamp
            assert data_out.end_time == data_in[-1].end_time

            assert sum([r.steps for r in data_in]) == data_out.steps

        # Cleanup
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=Steps, event_repo=depr_event_repository)

    async def test_load_sleep_should_pass(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        depr_event_repository: DeprEventRepository,
    ):
        # Arrange
        timestamp = PrimitiveTypesGenerator.generate_random_aware_datetime()
        delta = timedelta(seconds=PrimitiveTypesGenerator.generate_random_int(min_value=10, max_value=100))
        input_data = [
            LoadSleepInputBuilder()
            .with_timestamp(timestamp=timestamp + delta * i)
            .with_end_time(end_time=timestamp + delta * (i + 1))
            .build()
            for i in range(PrimitiveTypesGenerator.generate_random_int(max_value=10, min_value=1))
        ]
        metadata = MetadataInputBuilder().build()
        request_input = LoadSleepAPIRequestInput(documents=input_data, metadata=metadata)
        user, headers = await user_headers_factory()

        bucket = DaySplitter.create_day_time_bucket(entry_datetime=timestamp, time_limit=time(hour=18, minute=0))

        # Act
        request_url = DataCrudEndpointsUrls.SLEEP
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
        )

        # Assert
        assert response
        assert response.status_code == status.HTTP_200_OK, response.content

        payload = CommonDocumentsResponse[SleepAPIOutput](**response.json())
        assert payload
        sleep_event = payload.documents[0]
        assert sleep_event

        assert sleep_event.timestamp == bucket.timestamp
        assert sleep_event.end_time == bucket.end_time
        assert MetadataInputModel.map(model=sleep_event.metadata) == metadata

        # Duration Calculation
        expected_duration = int((bucket.end_time - bucket.timestamp).total_seconds())
        assert sleep_event.duration == expected_duration

        assert sleep_event.sleep_events
        summary = sleep_event.sleep_events[0].sleep_summary
        assert summary
        assert summary.events_count == len(input_data)

        for seconds, sleep_stage in (
            (summary.deep_seconds, SleepStage.DEEP),
            (summary.light_seconds, SleepStage.LIGHT),
            (summary.rem_seconds, SleepStage.REM),
            (summary.restless_seconds, SleepStage.RESTLESS),
        ):
            assert seconds == sum(
                [
                    int((s.end_time - s.timestamp).total_seconds())
                    for s in filter(lambda i: i.sleep_stage == sleep_stage, input_data)
                ]
            )

        assert summary.in_bed_seconds == sum([int((s.end_time - s.timestamp).total_seconds()) for s in input_data])
        assert summary.asleep_seconds == sum(
            [
                int((s.end_time - s.timestamp).total_seconds())
                for s in filter(
                    lambda i: i.sleep_stage not in (SleepStage.AWAKE, SleepStage.RESTLESS),
                    input_data,
                )
            ]
        )
        assert summary.awake_seconds == sum(
            [
                int((s.end_time - s.timestamp).total_seconds())
                for s in filter(lambda i: i.sleep_stage in (SleepStage.AWAKE, SleepStage.RESTLESS), input_data)
            ]
        )

        details = sleep_event.sleep_events[0].sleep_detail
        assert details
        assert len(details) == len(input_data)

        for detail in details:
            matching_input: LoadSleepInput = next(filter(lambda d: d.timestamp == detail.timestamp, input_data), None)
            assert matching_input
            assert detail.end_time == matching_input.end_time
            assert detail.stage == matching_input.sleep_stage
            assert detail.end_time == matching_input.end_time
            expected_duration = int((matching_input.end_time - matching_input.timestamp).total_seconds())
            assert detail.duration == expected_duration

        # Cleanup
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=Sleep, event_repo=depr_event_repository)
