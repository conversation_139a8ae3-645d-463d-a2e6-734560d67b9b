from datetime import datetime
from typing import List, <PERSON><PERSON>
from uuid import UUID, uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.common_data_response import CommonDataResponse
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.metadata import DataIntegrity, Organization
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.shared import CoordinatesModel
from services.data_service.api.urls import LocationEndpointUrls
from services.data_service.application.use_cases.list_location_use_case import ListLocationUseCaseOutputItem
from services.data_service.constants import API_FILTER_TIME_GTE, API_FILTER_TIME_LTE
from services.data_service.tests.api.common_calls import (
    _delete_user_documents,
)
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint


class TestLocationEndpoints:
    @pytest.fixture(scope="class")
    async def seed_list_location(self, depr_event_repository):
        # Setup
        user_uuid = uuid4()
        first_sample = Location(
            timestamp=datetime.fromisoformat("2023-10-16T12:00:00Z"),
            start_coordinates=CoordinatesModel(latitude=49.59, longitude=17.25),
            average_coordinates=CoordinatesModel(latitude=49.59, longitude=17.25),
            metadata=Metadata(user_uuid=user_uuid, data_integrity=DataIntegrity.MEDIUM, organization=Organization.LLIF),
        )
        second_sample = Location(
            timestamp=datetime.fromisoformat("2023-10-16T13:00:00Z"),
            start_coordinates=CoordinatesModel(latitude=49.59, longitude=17.25),
            average_coordinates=CoordinatesModel(latitude=49.59, longitude=17.25),
            metadata=Metadata(user_uuid=user_uuid, data_integrity=DataIntegrity.MEDIUM, organization=Organization.LLIF),
        )
        samples = await depr_event_repository.insert(models=[first_sample, second_sample])

        yield user_uuid, samples

        # Teardown
        await _delete_user_documents(user_uuid=user_uuid, data_schema=Location, event_repo=depr_event_repository)

    async def test_list_location_fetch_should_pass(self, seed_list_location: Tuple[UUID, List[Location]]):
        # Arrange
        user_uuid = seed_list_location[0]
        seed_location = seed_list_location[1]
        query_params = {
            API_FILTER_TIME_GTE: "2023-10-16T00:00:00Z",
            API_FILTER_TIME_LTE: "2023-10-17T00:00:00Z",
        }
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}

        request_url = join_as_url(base_url=LocationEndpointUrls.HISTORY, query_params=query_params)

        response = await _call_get_endpoint(headers=headers, request_url=request_url)

        assert response.status_code == status.HTTP_200_OK, response.json()
        response_model = CommonDataResponse[ListLocationUseCaseOutputItem](**response.json())
        assert [result.doc_id for result in sorted(response_model.Values, key=lambda r: r.timestamp)] == [
            location.doc_id for location in sorted(seed_location, key=lambda d: d.timestamp)
        ]


async def test_location_history_sample_data_fetch_should_pass(
    snapshot, data_service_common_query_params, seed_user_header
):
    request_url = join_as_url(LocationEndpointUrls.HISTORY, data_service_common_query_params)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, response.json()

    snapshot.assert_match(response.json())


async def test_location_movement_sample_data_fetch_should_pass(
    snapshot, data_service_common_query_params, seed_user_header
):
    request_url = join_as_url(LocationEndpointUrls.MOVEMENT, data_service_common_query_params)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, response.json()

    snapshot.assert_match(response.json())


async def test_location_place_sample_data_fetch_should_pass(
    snapshot, data_service_common_query_params, seed_user_header
):
    request_url = join_as_url(LocationEndpointUrls.PLACE, data_service_common_query_params)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, response.json()

    snapshot.assert_match(response.json())
