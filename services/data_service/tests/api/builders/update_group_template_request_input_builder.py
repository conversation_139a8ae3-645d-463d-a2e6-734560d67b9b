from __future__ import annotations

from typing import List, Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.data_service.api.v3.models.request.template.update_template_request_input import (
    UpdateTemplateAPIRequestInput,
)
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateGroupTemplateInputBoundaryItem,
    UpdateTemplateInputBoundaryItem,
)


class UpdateGroupTemplateRequestInputBuilder:
    def __init__(self):
        self.values: List[UpdateTemplateInputBoundaryItem] | None = None

    def build(self) -> UpdateTemplateAPIRequestInput:
        return UpdateTemplateAPIRequestInput(
            documents=self.values or UpdateGroupTemplateRequestInputItemBuilder().build_n()
        )

    def with_values(self, values: List[UpdateTemplateInputBoundaryItem]) -> UpdateGroupTemplateRequestInputBuilder:
        self.values = values
        return self


class UpdateGroupTemplateRequestInputItemBuilder:
    def __init__(self):
        self._name: str | None = None
        self._id: UUID | None = None
        self._template_ids: list[UUID] | None = None

    def build(self) -> UpdateGroupTemplateInputBoundaryItem:
        return UpdateGroupTemplateInputBoundaryItem(
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            template_ids=self._template_ids
            or [uuid4() for _ in range(PrimitiveTypesGenerator.generate_random_int(1, 5))],
            id=self._id or uuid4(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
        )

    def build_n(self, n: int | None = None) -> List[UpdateGroupTemplateInputBoundaryItem]:
        return [
            self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_template_ids(self, template_ids: Sequence[UUID]) -> Self:
        self._template_ids = template_ids
        return self
