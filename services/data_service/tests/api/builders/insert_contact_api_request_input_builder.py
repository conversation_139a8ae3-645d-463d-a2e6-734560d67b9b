from typing import Sequence

from services.data_service.api.v3.models.request.contact.insert_contact_api_request_input import (
    InsertContactAPIRequestInput,
)
from services.data_service.application.use_cases.contact.models.insert_contact_input_boundary import (
    InsertContactInput,
)


class InsertContactAPIRequestInputBuilder:
    def __init__(self):
        self._contacts = None

    def build(self) -> InsertContactAPIRequestInput:
        return InsertContactAPIRequestInput(documents=self._contacts or [])

    def with_contacts(self, contacts: Sequence[InsertContactInput]):
        self._contacts = contacts
        return self
