import random

from services.data_service.api.request_models.plans.load_temp_plan_api_request_input import LoadTempPlanAPIRequestInput
from services.data_service.tests.api.builders.load_temp_plan_input_builder import LoadTempPlanInputBuilder


class LoadTempPlanAPIRequestInputBuilder:
    def build(self) -> LoadTempPlanAPIRequestInput:
        load_plan_inputs = [LoadTempPlanInputBuilder().build() for _ in range(random.randint(1, 10))]
        return LoadTempPlanAPIRequestInput(documents=load_plan_inputs)
