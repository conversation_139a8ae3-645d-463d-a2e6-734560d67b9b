import random
from typing import Awaitable, Callable, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.enums.metadata import Organization
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.data_service.api.urls import DataSummaryEndpointUrls
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint


async def test_data_summary_endpoint_should_pass(snapshot, seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.USER_DATA,
        {"data_type": DataType.Steps.value, "organization": Organization.GOOGLE.value},
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    snapshot.assert_match(response.json())


async def test_data_summary_endpoint_no_data_should_return_no_content(user_headers_factory):
    user, headers = await user_headers_factory()
    request_url = join_as_url(
        DataSummaryEndpointUrls.USER_DATA,
        {"data_type": DataType.Steps.value, "organization": Organization.GOOGLE.value},
    )
    response = await _call_get_endpoint(request_url=request_url, headers=headers)
    assert response.status_code == status.HTTP_204_NO_CONTENT, f"got response {response.content}"


async def test_data_summary_endpoint_incorrect_type_should_return_unprocessable_entity(seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.USER_DATA,
        {"data_type": DataType.UserLogs.value, "organization": Organization.GOOGLE.value},
    )
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"got response {response.content}"


async def test_data_summary_endpoint_no_organization_filter_should_pass(snapshot, seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.USER_DATA,
        {"data_type": DataType.Steps.value, "organization": Organization.GOOGLE.value},
    )
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    snapshot.assert_match(response.json())


async def test_data_summary_endpoint_one_organization_filter_should_pass(snapshot, seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.USER_DATA,
        {
            "data_type": (DataType.HeartRate.value, DataType.RestingHeartRate.value),
            "organization": (Organization.LLIF.value, Organization.FITBIT.value),
        },
    )
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    snapshot.assert_match(response.json())


async def test_data_summary_endpoint_several_organizations_filters_should_pass(snapshot, seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.USER_DATA,
        {
            "data_type": DataType.Steps.value,
            "organization": (
                Organization.APPLE.value,
                Organization.FACEBOOK.value,
                Organization.GARMIN.value,
                Organization.GOOGLE.value,
            ),
        },
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    snapshot.assert_match(response.json())


async def test_tag_count_endpoint_returns_expected_value(
    snapshot,
    seed_user_header,
):
    request_url = join_as_url(
        DataSummaryEndpointUrls.TAG_COUNT,
        {"data_type": DataType.DiaryEvents.value},
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    snapshot.assert_match(response.json())


async def test_tag_count_endpoint_no_data_found_should_return_no_content(seed_user_header):
    request_url = join_as_url(DataSummaryEndpointUrls.TAG_COUNT, {"data_type": DataType.HeartRate.value})

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_204_NO_CONTENT, f"got response {response.content}"


async def test_tag_count_endpoint_invalid_data_type_should_return_bad_request(seed_user_header):
    request_url = join_as_url(DataSummaryEndpointUrls.TAG_COUNT, {"data_type": DataType.Pollen.value})

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"got response {response.content}"


async def test_statistics_endpoint_returns_expected_value_steps(snapshot, seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.STATISTICS,
        {
            "data_type": DataType.Steps.value,
            "organization": Organization.GOOGLE.value,
            "time_gte": "2018-12-18T14:08:44Z",
            "time_lte": "2024-12-18T14:08:44Z",
        },
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    snapshot.assert_match(response.json())


async def test_statistics_endpoint_returns_expected_value_sleep(snapshot, seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.STATISTICS,
        {
            "data_type": DataType.Sleep.value,
            "time_gte": "2018-12-18T14:08:44Z",
            "time_lte": "2024-12-18T14:08:44Z",
        },
    )
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    snapshot.assert_match(response.json())


async def test_statistics_endpoint_returns_not_supported_data_type_should_return_bad_request(seed_user_header):
    request_url = join_as_url(
        DataSummaryEndpointUrls.STATISTICS,
        {
            "data_type": DataType.DiaryEvents.value,
            "time_gte": "2018-12-18T14:08:44Z",
            "time_lte": "2022-12-18T14:08:44Z",
        },
    )
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_400_BAD_REQUEST, f"got response {response.content}"


async def test_body_parts_endpoint_should_pass(snapshot, seed_user_header):
    request_url = DataSummaryEndpointUrls.BODY_PARTS
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    snapshot.assert_match(response.json())


class TestDataSummaryEndpoints:
    @pytest.fixture
    async def user_with_events_v3(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[Sequence[Event], MemberUser]:
        user: MemberUser = await user_factory()
        events = EventBuilder().with_owner_id(owner_id=user.user_uuid).build_all()

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in events])

    async def test_user_data_summary_endpoint_passes_with_events_v3(
        self, user_with_events_v3: tuple[Sequence[Event], MemberUser]
    ):
        # Arrange
        existing_events, user = user_with_events_v3
        dts = [e.value for e in EventV3Type]
        expected_origins = {e.metadata.origin.value for e in existing_events}
        expected_origins = random.sample(population=list(expected_origins), k=random.randint(1, len(expected_origins)))

        request_url = join_as_url(
            DataSummaryEndpointUrls.USER_DATA,
            {
                "data_type": dts,
                "organization": expected_origins,  ## TODO change this endpoint to origin when v2 are migrated
            },
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        output = response.json()
        assert output
        data = output["data"]
        for dt in dts:
            found_orgs = {e["organization"] for e in data if e["data_type"] == dt}
            assert found_orgs.issubset(expected_origins)
            for o in found_orgs:
                found = any((i for i in data if i["data_type"] == dt and i["organization"] == o))
                assert found
