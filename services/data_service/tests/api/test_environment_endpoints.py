from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import AsyncGenerator
from uuid import UUID, uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDataResponse
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.metadata import DataIntegrity, Organization
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.shared import CoordinatesModel
from services.data_service.api.output_models.environment.air_quality_aggregation_api_output import (
    AirQualityAggregationAPIOutput,
)
from services.data_service.api.output_models.environment.pollen_aggregation_api_output import PollenAggregationAPIOutput
from services.data_service.api.output_models.environment.weather_aggregation_api_output import (
    WeatherAggregationAPIOutput,
)
from services.data_service.api.urls import EnvironmentV2EndpointUrls
from services.data_service.application.models.air_quality_bucket import AirQualityBucket
from services.data_service.application.models.pollen_bucket import PollenBucket
from services.data_service.application.models.weather_bucket import WeatherBucket
from services.data_service.application.use_cases.environment.aggregate_air_quality_use_case import (
    AirQualityAggregationBucket,
)
from services.data_service.application.use_cases.environment.aggregate_pollen_use_case import PollenAggregationBucket
from services.data_service.application.use_cases.environment.aggregate_weather_use_case import WeatherAggregationBucket
from services.data_service.constants import API_FILTER_INTERVAL, API_FILTER_TIME_GTE, API_FILTER_TIME_LTE
from services.data_service.tests.api.common_calls import (
    _delete_user_documents,
)
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint

_PLACES = [
    (40.7128, -74.0060),  # New York
    (51.5074, -0.1278),  # London
    (1.3521, 103.8198),  # Singapore
    (35.6762, 139.6503),  # Tokyo
    (37.7749, -122.4194),  # San Francisco
    (-33.94, 18.44),  # Cape Town
    (-33.78, 151.15),  # Sydney
    (30.2672, -97.7431),  # Austin
    (39.9042, 116.4074),  # Beijing
    (50.0755, 14.4378),  # Prague
    (-15.7975, -47.8919),  # Brasilia
]


class TestEnvironmentEndpointsV2:
    @pytest.fixture(scope="class")
    def expectedAirQualityBucket(self) -> AirQualityAggregationBucket:
        timestamp = datetime.fromisoformat("2023-07-16T12:00:00Z")
        return AirQualityAggregationBucket(
            timestamp=timestamp,
            pm10=27.7,
            pm25=20.8,
            no2=2.6,
            so2=5.8,
            co=194.0,
            o3=119.0,
            aqi_eu=53.0,
            aqi_us=55.0,
            aqi_gb=4.0,
            coordinates=CoordinatesModel(lat=49.55, lon=17.25),
            user_coordinates=CoordinatesModel(lat=49.59, lon=17.25),
        )

    @pytest.fixture(scope="class")
    def expectedWeatherBucket(self) -> WeatherAggregationBucket:
        return WeatherAggregationBucket(
            timestamp=datetime.fromisoformat("2023-07-16T12:00:00Z"),
            temperature=30.4,
            temperature_feels_like=30.6,
            humidity=43.59,
            wind_speed=10.2,
            wind_gust=22.3,
            wind_degree=19.0,
            cloud_cover=47.0,
            uv=0.0,
            pressure=1013.6,
            visibility=31.4,
            precipitation=0.0,
            coordinates=CoordinatesModel(lat=49.596, lon=17.254),
            user_coordinates=CoordinatesModel(lat=49.59, lon=17.25),
        )

    @pytest.fixture(scope="class")
    def expectedPollenBucket(self) -> PollenAggregationBucket:
        return PollenAggregationBucket(
            timestamp=datetime.fromisoformat("2023-07-16T12:00:00Z"),
            tree_count=0.0,
            weed_count=1,
            grass_count=9,
            coordinates=CoordinatesModel(lat=49.55, lon=17.25),
            user_coordinates=CoordinatesModel(lat=49.59, lon=17.25),
        )

    @pytest.fixture(scope="class")
    async def user_uuid(self, depr_event_repository: DeprEventRepository) -> AsyncGenerator[UUID, None]:
        # Setup
        user_uuid = uuid4()
        location = Location(
            timestamp=datetime.fromisoformat("2023-07-16T12:00:00Z"),
            start_coordinates=CoordinatesModel(lat=49.59, lon=17.25),
            average_coordinates=CoordinatesModel(lat=49.59, lon=17.25),
            metadata=Metadata(user_uuid=user_uuid, data_integrity=DataIntegrity.MEDIUM, organization=Organization.LLIF),
        )

        location = await depr_event_repository.insert(models=[location], force_strong_consistency=True)

        yield user_uuid

        # Teardown
        await _delete_user_documents(user_uuid=user_uuid, data_schema=Location, event_repo=depr_event_repository)

    @pytest.mark.integration
    async def test_air_quality_sample_data_fetch_should_pass(
        self,
        user_uuid: UUID,
        expectedAirQualityBucket: AirQualityAggregationBucket,
    ):
        # Arrange
        time_gte = "2023-07-16T00:00:00Z"
        time_lte = "2023-07-17T00:00:00Z"

        params = {
            API_FILTER_TIME_GTE: time_gte,
            API_FILTER_TIME_LTE: time_lte,
            API_FILTER_INTERVAL: "1h",
        }
        request_url = join_as_url(base_url=EnvironmentV2EndpointUrls.AIR_QUALITY, query_params=params)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        output = AirQualityAggregationAPIOutput(**response.json())

        assert len(output.data) == 1

        # coordinates can be flaky for the environment, we cannot make sure that the sensor is always the same
        output.data[0].coordinates = expectedAirQualityBucket.coordinates

        # check if the returned data is within the bounds of the time input
        timestamp = output.data[0].timestamp
        assert timestamp >= datetime.fromisoformat(time_gte) and timestamp <= datetime.fromisoformat(time_lte)

        assert output.data[0] == expectedAirQualityBucket

    @pytest.mark.integration
    async def test_weather_sample_data_fetch_should_pass(
        self,
        user_uuid: UUID,
        expectedWeatherBucket: WeatherAggregationBucket,
    ):
        # Arrange
        time_gte = "2023-07-16T00:00:00Z"
        time_lte = "2023-07-17T00:00:00Z"

        params = {
            API_FILTER_TIME_GTE: time_gte,
            API_FILTER_TIME_LTE: time_lte,
            API_FILTER_INTERVAL: "1h",
        }
        request_url = join_as_url(EnvironmentV2EndpointUrls.WEATHER, query_params=params)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK

        output = WeatherAggregationAPIOutput(**response.json())
        assert len(output.data) == 1

        # coordinates can be flaky for the environment, we cannot make sure that the sensor is always the same
        output.data[0].coordinates = expectedWeatherBucket.coordinates

        # check if the returned data is within the bounds of the time input
        timestamp = output.data[0].timestamp
        assert timestamp >= datetime.fromisoformat(time_gte) and timestamp <= datetime.fromisoformat(time_lte)

        assert output.data[0] == expectedWeatherBucket

    @pytest.mark.integration
    async def test_pollen_sample_data_fetch_should_pass(
        self,
        user_uuid: UUID,
        expectedPollenBucket: PollenAggregationBucket,
    ):
        # Arrange
        time_gte = "2023-07-16T00:00:00Z"
        time_lte = "2023-07-17T00:00:00Z"

        params = {
            API_FILTER_TIME_GTE: time_gte,
            API_FILTER_TIME_LTE: time_lte,
            API_FILTER_INTERVAL: "1h",
        }
        request_url = join_as_url(EnvironmentV2EndpointUrls.POLLEN, query_params=params)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK

        output = PollenAggregationAPIOutput(**response.json())
        assert len(output.data) == 1

        # coordinates can be flaky for the environment, we cannot make sure that the sensor is always the same
        output.data[0].coordinates = expectedPollenBucket.coordinates

        # check if the returned data is within the bounds of the time input
        timestamp = output.data[0].timestamp
        assert timestamp >= datetime.fromisoformat(time_gte) and timestamp <= datetime.fromisoformat(time_lte)

        assert output.data[0] == expectedPollenBucket

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "lat,lon",
        _PLACES,
    )
    async def test_forecast_aq_data_fetch_should_pass(
        self,
        lat: float,
        lon: float,
        user_uuid: UUID,
    ):
        hours = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=48)
        current_time = datetime.now(PrimitiveTypesGenerator.generate_random_zoneinfo())
        request_url = join_as_url(
            EnvironmentV2EndpointUrls.FORECAST_AIR_QUALITY,
            query_params={
                "timestamp": current_time,
                "end_time": current_time + timedelta(hours=hours),
                "lat": lat,
                "lon": lon,
            },
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}
        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)
        # Assert
        assert response.status_code == status.HTTP_200_OK
        output = CommonDataResponse[AirQualityBucket](**response.json())
        assert output
        assert len(output.data) >= hours
        assert output.data[0].timestamp.tzinfo.utcoffset(dt=output.data[0].timestamp) == current_time.tzinfo.utcoffset(
            current_time
        )

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "lat,lon",
        _PLACES,
    )
    async def test_forecast_weather_data_fetch_should_pass(self, lat: float, lon: float, user_uuid: UUID):
        hours = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=48)
        current_time = datetime.now(PrimitiveTypesGenerator.generate_random_zoneinfo())
        request_url = join_as_url(
            EnvironmentV2EndpointUrls.FORECAST_WEATHER,
            query_params={
                "timestamp": current_time,
                "end_time": current_time + timedelta(hours=hours),
                "lat": lat,
                "lon": lon,
            },
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}
        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)
        # Assert
        assert response.status_code == status.HTTP_200_OK
        output = CommonDataResponse[WeatherBucket](**response.json())
        assert output
        assert len(output.data) >= hours
        assert output.data[0].timestamp.tzinfo.utcoffset(dt=output.data[0].timestamp) == current_time.tzinfo.utcoffset(
            current_time
        )

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "lat,lon",
        _PLACES,
    )
    async def test_forecast_pollen_data_fetch_should_pass(self, lat: float, lon: float, user_uuid: UUID):
        hours = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=48)
        current_time = datetime.now(PrimitiveTypesGenerator.generate_random_zoneinfo())
        request_url = join_as_url(
            EnvironmentV2EndpointUrls.FORECAST_POLLEN,
            query_params={
                "timestamp": current_time,
                "end_time": current_time + timedelta(hours=hours),
                "lat": lat,
                "lon": lon,
            },
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}
        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)
        # Assert
        assert response.status_code == status.HTTP_200_OK
        output = CommonDataResponse[PollenBucket](**response.json())
        assert output
        assert len(output.data) >= hours
        assert output.data[0].timestamp.tzinfo.utcoffset(dt=output.data[0].timestamp) == current_time.tzinfo.utcoffset(
            current_time
        )

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "lat,lon",
        _PLACES,
    )
    async def test_air_quality_data_spacetime_fetch_should_pass(self, lat: float, lon: float, user_uuid: UUID):
        hours = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=48)
        current_time = datetime.now(PrimitiveTypesGenerator.generate_random_zoneinfo())
        request_url = join_as_url(
            EnvironmentV2EndpointUrls.AIR_QUALITY_BY_SPACETIME,
            query_params={
                "timestamp": current_time - timedelta(hours=hours),
                "end_time": current_time,
                "lat": lat,
                "lon": lon,
            },
        )
        # Act
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}
        response = await _call_get_endpoint(request_url=request_url, headers=headers)
        # Assert
        assert response.status_code == status.HTTP_200_OK
        output: CommonDataResponse[AirQualityBucket] = CommonDataResponse[AirQualityBucket](**response.json())
        assert output
        assert len(output.data) >= hours
        assert output.data[0].timestamp.tzinfo.utcoffset(dt=output.data[0].timestamp) == current_time.tzinfo.utcoffset(
            current_time
        )

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "lat,lon",
        _PLACES,
    )
    async def test_weather_data_spacetime_fetch_should_pass(self, lat: float, lon: float, user_uuid: UUID):
        hours = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=48)
        current_time = datetime.now(PrimitiveTypesGenerator.generate_random_zoneinfo())
        request_url = join_as_url(
            EnvironmentV2EndpointUrls.WEATHER_BY_SPACETIME,
            query_params={
                "timestamp": current_time - timedelta(hours=hours),
                "end_time": current_time,
                "lat": lat,
                "lon": lon,
            },
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}
        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)
        # Assert
        assert response.status_code == status.HTTP_200_OK
        output: CommonDataResponse[WeatherBucket] = CommonDataResponse[WeatherBucket](**response.json())
        assert output
        assert len(output.data) >= hours
        assert output.data[0].timestamp.tzinfo.utcoffset(dt=output.data[0].timestamp) == current_time.tzinfo.utcoffset(
            current_time
        )

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "lat,lon",
        _PLACES,
    )
    async def test_pollen_data_spacetime_fetch_should_pass(self, lat: float, lon: float, user_uuid: UUID):
        hours = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=48)
        current_time = datetime.now(PrimitiveTypesGenerator.generate_random_zoneinfo())
        request_url = join_as_url(
            EnvironmentV2EndpointUrls.POLLEN_BY_SPACETIME,
            query_params={
                "timestamp": current_time - timedelta(hours=hours),
                "end_time": current_time,
                "lat": lat,
                "lon": lon,
            },
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_uuid)}"}
        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)
        # Assert
        assert response.status_code == status.HTTP_200_OK
        output: CommonDataResponse[PollenBucket] = CommonDataResponse[PollenBucket](**response.json())
        assert output
        assert len(output.data) >= hours
        assert output.data[0].timestamp.tzinfo.utcoffset(dt=output.data[0].timestamp) == current_time.tzinfo.utcoffset(
            current_time
        )
