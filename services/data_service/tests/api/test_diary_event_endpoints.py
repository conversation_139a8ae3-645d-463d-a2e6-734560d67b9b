from typing import List
from uuid import UUID

import pytest
from starlette import status

from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.schemas.diary_events import (
    Diary<PERSON>vent<PERSON>ustomDataFields,
    <PERSON><PERSON><PERSON>sFields,
    DiaryEventsPlanExtensionFields,
    DiaryEventsSchema,
)
from services.data_service.api.urls import DiaryEndpointUrls
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint


async def test_list_events_data_fetch_should_pass(
    data_service_common_query_params_detail,
    seed_user_header,
):
    request_url = join_as_url(DiaryEndpointUrls.EVENTS, data_service_common_query_params_detail)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)
    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    result = response.json()
    for v in result["Values"]:
        assert DiaryEventsSchema(**v)


@pytest.mark.parametrize(
    "only_custom_data",
    (["test"], ["hello"]),
)
async def test_list_events_fetch_only_custom_value_should_pass(
    only_custom_data: List[str], seed_user_header, data_service_common_query_params_detail
):
    request_url = join_as_url(
        DiaryEndpointUrls.EVENTS, {"only_custom_data": only_custom_data} | data_service_common_query_params_detail
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    results = response.json()
    for result in results["Values"]:
        assert result
        for custom_data in result[DiaryEventsFields.CUSTOM_DATA]:
            assert any([custom_data[DiaryEventCustomDataFields.KEY] == key for key in only_custom_data])


@pytest.mark.parametrize(
    "only_types",
    ([DiaryEventType.EVENT.value], [DiaryEventType.FOOD.value, DiaryEventType.SUPPLEMENT.value]),
)
async def test_list_events_fetch_only_types_should_pass(
    only_types: List[str], seed_user_header, data_service_common_query_params_detail
):
    request_url = join_as_url(
        base_url=DiaryEndpointUrls.EVENTS,
        query_params={"only_types": only_types} | data_service_common_query_params_detail,
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    results = response.json()
    for result in results["Values"]:
        assert result
        assert result[DiaryEventsFields.TYPE] in only_types


@pytest.mark.parametrize("only_plan_ids", ([UUID("588575b0-2e57-4a63-b08e-22cf14ffe6ae")],))
async def test_list_events_fetch_only_plan_ids_should_pass(
    only_plan_ids: List[UUID],
    seed_user_header,
    data_service_common_query_params_detail,
):
    request_url = join_as_url(
        base_url=DiaryEndpointUrls.EVENTS,
        query_params={"only_plan_ids": only_plan_ids} | data_service_common_query_params_detail,
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    results = response.json()
    for result in results["Values"]:
        assert result
        assert UUID(result[DiaryEventsFields.PLAN_EXTENSION][DiaryEventsPlanExtensionFields.PLAN_ID]) in only_plan_ids


@pytest.mark.parametrize(
    "only_names",
    (["this is event name"]),
)
async def test_list_events_fetch_only_names_should_pass(
    only_names: List[str],
    seed_user_header,
    data_service_common_query_params_detail,
):
    request_url = join_as_url(
        DiaryEndpointUrls.EVENTS, {"only_names": only_names} | data_service_common_query_params_detail
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    results = response.json()
    for result in results["Values"]:
        assert result
        assert result[DiaryEventsFields.NAME] in only_names


async def test_aggregated_events_fetch_should_pass(
    snapshot,
    data_service_common_query_params,
    seed_user_header,
):
    request_url = join_as_url(DiaryEndpointUrls.EVENTS, data_service_common_query_params)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    result = response.json()
    snapshot.assert_match(result)


@pytest.mark.parametrize(
    "only_names",
    (["this is event name"]),
)
async def test_aggregated_events_fetch_only_names_should_pass(
    snapshot,
    only_names: List[str],
    seed_user_header,
    data_service_common_query_params,
):
    request_url = join_as_url(DiaryEndpointUrls.EVENTS, {"only_names": only_names} | data_service_common_query_params)

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    result = response.json()
    snapshot.assert_match(result)


@pytest.mark.parametrize(
    "only_types",
    ([DiaryEventType.EVENT.value], [DiaryEventType.FOOD.value, DiaryEventType.SUPPLEMENT.value]),
)
async def test_aggregated_events_fetch_only_types_should_pass(
    snapshot, only_types: List[str], seed_user_header, data_service_common_query_params
):
    request_url = join_as_url(
        base_url=DiaryEndpointUrls.EVENTS,
        query_params={"only_types": only_types} | data_service_common_query_params,
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    result = response.json()
    snapshot.assert_match(result)


@pytest.mark.parametrize("only_plan_ids", ([UUID("588575b0-2e57-4a63-b08e-22cf14ffe6ae")],))
async def test_aggregated_events_fetch_only_plan_ids_should_pass(
    snapshot,
    only_plan_ids: List[UUID],
    seed_user_header,
    data_service_common_query_params,
):
    request_url = join_as_url(
        base_url=DiaryEndpointUrls.EVENTS,
        query_params={"only_plan_ids": only_plan_ids} | data_service_common_query_params,
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    result = response.json()
    snapshot.assert_match(result)


@pytest.mark.parametrize(
    "only_custom_data",
    (["test"], ["hello"]),
)
async def test_aggregated_events_fetch_only_custom_value_should_pass(
    snapshot,
    only_custom_data: List[str],
    seed_user_header,
    data_service_common_query_params,
):
    request_url = join_as_url(
        DiaryEndpointUrls.EVENTS, {"only_custom_data": only_custom_data} | data_service_common_query_params
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    result = response.json()
    snapshot.assert_match(result)


@pytest.mark.parametrize(
    "standard",
    (
        True,
        False,
    ),
)
async def test_unique_events_sample_data_fetch_should_pass(
    standard: bool,
    seed_user_header,
):
    request_url = join_as_url(DiaryEndpointUrls.EVENTS_UNIQUE, {"standard": standard})

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)
    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    results = response.json()
    for result in results["results"]:
        event_schema = DiaryEventsSchema(**result)
        assert event_schema.is_standard == standard


@pytest.mark.parametrize(
    "only_custom_data, standard",
    [
        (False, False),
        (True, False),
    ],
)
async def test_fetch_unique_events_with_only_custom_data_filter_should_return_event(
    seed_user_header,
    only_custom_data: bool,
    standard: bool,
):
    request_url = join_as_url(
        base_url=DiaryEndpointUrls.EVENTS_UNIQUE,
        query_params={"only_custom_data": only_custom_data, "standard": standard},
    )

    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)
    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"

    results = response.json()
    for result in results["results"]:
        event_schema = DiaryEventsSchema(**result)
        if only_custom_data:
            assert event_schema.custom_data
        assert event_schema.is_standard == standard
