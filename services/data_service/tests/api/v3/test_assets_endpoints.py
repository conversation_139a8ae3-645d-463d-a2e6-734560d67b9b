from typing import Callable, <PERSON><PERSON>

import pytest
from pydantic import HttpUrl
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.assets import Assets
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.object_storage_service import ObjectStorageService
from services.base.application.utils.urls import join_as_url
from services.base.domain.annotated_types import AssetId
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.api.urls import AssetsEndpointUrls
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint


class TestAssetsEndpoints:
    class AssetFixture(BaseDataModel):
        asset_id: AssetId
        asset_url: HttpUrl
        content: bytes

    @pytest.fixture(scope="function")
    async def user_and_asset(
        self, object_storage_service: ObjectStorageService, user_factory: Callable[[], MemberUser]
    ) -> <PERSON><PERSON>[MemberUser, AssetFixture]:
        user = await user_factory()
        container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)
        await object_storage_service.create_container(container_name=container_name)

        asset_id = Assets.generate_asset_id()
        asset_path = Assets.generate_asset_path(asset_id=asset_id)
        content = PrimitiveTypesGenerator.generate_random_string().encode()
        await object_storage_service.create_object(container_name=container_name, object_name=asset_path, data=content)

        asset_url = await object_storage_service.get_object_url(container_name=container_name, object_name=asset_path)
        yield user, self.AssetFixture(asset_url=asset_url, asset_id=asset_id, content=content)

        await object_storage_service.delete_container(container_name=container_name)

    async def test_fetch_asset_by_id_valid_id_should_pass(self, user_and_asset: Tuple[MemberUser, AssetFixture]):
        user = user_and_asset[0]
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        asset_fixture = user_and_asset[1]
        asset_id = asset_fixture.asset_id
        asset_content = asset_fixture.content

        request_url = join_as_url(base_url=AssetsEndpointUrls.BY_ID, query_params={"asset_id": asset_id})

        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        assert response.status_code == status.HTTP_200_OK
        assert response.content == asset_content

    async def test_fetch_asset_url_valid_id_should_pass(self, user_and_asset: Tuple[MemberUser, AssetFixture]):
        user = user_and_asset[0]
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        asset_fixture = user_and_asset[1]
        asset_id = asset_fixture.asset_id
        asset_url = asset_fixture.asset_url

        assets_ids = [asset_id]
        request_url = join_as_url(base_url=AssetsEndpointUrls.URL, query_params={"asset_ids": assets_ids})

        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        assert result
        assert asset_id in result["assets"]
        assert result["assets"][asset_id] == str(asset_url)
