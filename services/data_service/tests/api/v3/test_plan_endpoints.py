import itertools
import json
import random
from datetime import datetime, timedelta, timezone
from typing import Callable, Sequence

import pytest
from dateutil.rrule import DAILY
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.database.models.sorts import SortOrder
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.plan import Plan, PlanFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.tests.domain.builders.plan_builder import PlanBuilder, PlanStreakBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.group_template_builder import GroupTemplateBuilder
from services.base.type_resolver import TypeResolver
from services.data_service.api.urls import (
    PlanEndpointUrls,
)
from services.data_service.api.v3.models.request.plan.complete_plans_api_request_input import (
    CompletePlansAPIRequestInput,
)
from services.data_service.api.v3.models.request.plan.insert_plans_api_request_input import InsertPlansAPIRequestInput
from services.data_service.api.v3.models.request.plan.update_plans_api_request_input import UpdatePlansAPIRequestInput
from services.data_service.api.v3.models.response.plan.complete_plan_api_output import CompletePlanAPIOutput
from services.data_service.api.v3.models.response.plan.plan_api_output import PlanAPIOutput
from services.data_service.application.use_cases.plans.models.complete_plans_input_boundary import CompletePlanInput
from services.data_service.application.use_cases.plans.models.insert_plans_input_boundary import InsertPlanInput
from services.data_service.application.use_cases.plans.models.update_plan_input_boundary import UpdatePlanInput
from services.data_service.tests.api.builders.insert_plan_input_builder import InsertPlanInputBuilder
from services.data_service.tests.api.builders.insert_plans_api_request_input_builder import (
    InsertPlansAPIRequestInputBuilder,
)
from services.data_service.tests.api.builders.search_plans_request_input_builder import SearchPlansRequestInputBuilder
from services.data_service.tests.api.common_rpc_calls import (
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.tests.api.utils.test_utils import TestUtils


class TestPlanCRUD:
    @pytest.fixture
    async def user_with_templates_and_plans(
        self,
        plan_repo: PlanRepository,
        template_repo: TemplateRepository,
        user_factory: Callable[[], MemberUser],
    ) -> tuple[Sequence[Plan], MemberUser, Sequence[EventTemplate]]:
        user: MemberUser = await user_factory()
        random_input_origin = random.choice([Origin(o.value) for o in InsertableOrigin])
        event_templates = (
            EventTemplateBuilder()
            .with_origin(origin=random_input_origin)
            .with_archived_at(False)
            .with_owner_id(owner_id=user.user_uuid)
            .build_n(n=20)
        )
        group_templates = [
            GroupTemplateBuilder()
            .with_archived_at(archived_at=False)
            .with_owner_id(owner_id=user.user_uuid)
            .with_template_ids([t.id for t in random.sample(event_templates, random.randint(1, 5))])
            .build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
        ]

        templates_to_insert = [*event_templates, *group_templates]

        inserted_templates = await template_repo.insert(templates=templates_to_insert, force_strong_consistency=True)
        plans_to_insert = [
            PlanBuilder().with_template_id(template_id=t.id).with_owner_id(owner_id=user.user_uuid).build()
            for t in inserted_templates
        ]
        random.choice(plans_to_insert).archived_at = None

        inserted_plans = await plan_repo.insert(plans=plans_to_insert, force_strong_consistency=True)

        yield inserted_plans, user, inserted_templates

        # Teardown
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])
        await plan_repo.delete_by_id(ids=[p.id for p in inserted_plans])

    async def test_insert_plans_should_pass(self, user_with_templates_and_plans, plan_repo: PlanRepository):
        # Arrange
        existing_plans, user, existing_templates = user_with_templates_and_plans
        template_ids = [t.id for t in existing_templates]
        load_plan_request = (
            InsertPlansAPIRequestInputBuilder()
            .with_plans(
                plans=[
                    InsertPlanInputBuilder().with_template_id(template_id=random.choice(template_ids)).build()
                    for _ in range(1, 10)
                ]
            )
            .build()
        )
        body_dict = json.loads(load_plan_request.model_dump_json(by_alias=True))
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=PlanEndpointUrls.BASE, json=body_dict, headers=headers, retry=False
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        response_model = CommonDocumentsResponse[PlanAPIOutput](**response.json())
        assert len(response_model.documents) == len(load_plan_request.documents)

        for plan, expected_plan in zip(
            sorted(response_model.documents, key=lambda p: p.name),
            sorted(load_plan_request.documents, key=lambda p: p.name),
        ):
            if expected_plan.next_scheduled_at:
                expected_next_schedule = expected_plan.next_scheduled_at
            else:
                rule = CustomRRule.from_rrule(rule=str(expected_plan.recurrence))
                expected_next_schedule = rule.after(rule.started_at)

            assert not plan.archived_at
            assert plan.type == expected_plan.type
            assert plan.name == expected_plan.name
            assert plan.tags == expected_plan.tags
            assert expected_plan.recurrence == plan.recurrence
            assert plan.first_completed_at is None
            assert plan.next_scheduled_at == expected_next_schedule
            assert plan.template_id == expected_plan.template_id
            assert plan.priority == expected_plan.priority
            assert plan.prompt == expected_plan.prompt
            assert plan.note == expected_plan.note
            assert plan.tags == expected_plan.tags
            assert plan.is_urgent == expected_plan.is_urgent
            assert plan.is_absolute_schedule == expected_plan.is_absolute_schedule
            assert plan.is_confirmation_required == expected_plan.is_confirmation_required
            assert plan.streak == expected_plan.streak
            assert TestUtils.is_date_within_one_minute(plan.system_properties.created_at)

        # Teardown
        await plan_repo.delete_by_id([p.id for p in response_model.documents])

    async def test_insert_duplicated_plans_should_raise_bad_request(self, user_with_templates_and_plans):
        # Arrange
        existing_plans, user, _ = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            base_url=PlanEndpointUrls.BASE, query_params={"plan_ids": [p.id for p in existing_plans]}
        )

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json=json.loads(
                InsertPlansAPIRequestInput(
                    documents=[InsertPlanInput(**p.model_dump()) for p in existing_plans]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response: {response.json()}"

    async def test_insert_plans_invalid_recurrence_fails(
        self, plan_repo: PlanRepository, user_with_templates_and_plans
    ):
        # Arrange
        _, user, existing_templates = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        plan = (
            PlanBuilder()
            .with_template_id(template_id=random.choice([t.id for t in existing_templates]))
            .with_owner_id(owner_id=user.user_uuid)
            .with_archived_at(archived_at=False)
            .build()
            .model_dump()
        )

        plan_with_count = plan | {
            PlanFields.RECURRENCE: "DTSTART:2024-11-25T15:58:00-05:00\nRRULE:FREQ=YEARLY;INTERVAL=0"
        }

        plan_incorrect_interval = plan | {
            PlanFields.RECURRENCE: "DTSTART:2024-11-25T15:58:00-05:00\nRRULE:FREQ=YEARLY;COUNT=1"
        }

        plan_not_tz_aware = plan | {PlanFields.RECURRENCE: "DTSTART:2024-11-25T15:58:00\nRRULE:FREQ=YEARLY"}

        # Act
        for plan in (plan_with_count, plan_incorrect_interval, plan_not_tz_aware):
            response = await _call_post_endpoint(
                request_url=PlanEndpointUrls.BASE,
                json=json.loads(json.dumps({"documents": [plan]}, default=str)),
                headers=headers,
                retry=False,
            )

            # Assert
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"response: {response.json()}"

    async def test_archive_plans_should_pass(self, user_with_templates_and_plans):
        # Arrange
        existing_plans, user, _ = user_with_templates_and_plans
        existing_not_archived_plans = [p for p in existing_plans if not p.archived_at]
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            base_url=PlanEndpointUrls.ARCHIVE,
            query_params={"plan_ids": [p.id for p in existing_not_archived_plans]},
        )

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        archived_plans = CommonDocumentsResponse[PlanAPIOutput](**response.json()).documents

        assert len(archived_plans) == len(existing_not_archived_plans)
        for existing_plan, archived_plan in zip(
            (sorted(existing_not_archived_plans, key=lambda t: t.id)), (sorted(archived_plans, key=lambda t: t.id))
        ):
            assert existing_plan.id == archived_plan.id
            assert not existing_plan.archived_at
            assert archived_plan.archived_at
            assert TestUtils.is_date_within_one_minute(date=archived_plan.archived_at)

    async def test_search_plans_endpoint_should_pass(self, user_with_templates_and_plans):
        # Arrange
        existing_plans, user, _ = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        request_builder = SearchPlansRequestInputBuilder().with_limit(len(existing_plans))

        # Act
        response = await _call_post_endpoint(
            request_url=PlanEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        actual_plans = CommonDocumentsResponse[PlanAPIOutput](**response.json()).documents
        assert len(actual_plans) == len(existing_plans)

        expected_plan: Plan
        actual_plan: PlanAPIOutput
        for actual_plan, expected_plan in zip(
            sorted(actual_plans, key=lambda p: p.id), sorted(existing_plans, key=lambda p: p.id)
        ):
            assert actual_plan.type == expected_plan.type
            assert actual_plan.name == expected_plan.name
            assert actual_plan.tags == expected_plan.tags
            if expected_plan.recurrence:
                assert actual_plan.recurrence == str(expected_plan.recurrence)
            else:
                assert actual_plan.recurrence is None
            assert actual_plan.first_completed_at == expected_plan.first_completed_at
            assert actual_plan.next_scheduled_at == expected_plan.next_scheduled_at
            assert actual_plan.template_id == expected_plan.template_id
            assert actual_plan.priority == expected_plan.priority
            assert actual_plan.note == expected_plan.note
            assert actual_plan.tags == expected_plan.tags
            assert actual_plan.prompt == expected_plan.prompt
            assert actual_plan.is_urgent == expected_plan.is_urgent
            assert actual_plan.is_confirmation_required == expected_plan.is_confirmation_required
            assert actual_plan.system_properties.created_at == expected_plan.system_properties.created_at
            assert actual_plan.archived_at == expected_plan.archived_at
            assert actual_plan.is_absolute_schedule == expected_plan.is_absolute_schedule

    async def test_search_plans_endpoint_sort_by_priority_should_pass(self, user_with_templates_and_plans):
        # Arrange
        existing_plans, user, _ = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_builder = (
            SearchPlansRequestInputBuilder()
            .with_limit(len(existing_plans))
            .with_sort(sort=SortRequestInput(order=SortOrder.DESCENDING, field_name=PlanFields.PRIORITY))
        )

        # Act
        response = await _call_post_endpoint(
            request_url=PlanEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        returned_plans = CommonDocumentsResponse[PlanAPIOutput](**response.json()).documents

        assert len(returned_plans) == len(existing_plans)
        assert sorted(returned_plans, key=lambda p: p.priority, reverse=True) == returned_plans

    async def test_update_plans_by_id_should_pass(self, user_with_templates_and_plans):
        # Arrange
        existing_plans, user, _ = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        non_archived_plans = [p for p in existing_plans if not p.archived_at]

        input_plans = []
        for plan in non_archived_plans:
            rand_rec = PrimitiveTypesGenerator.generate_random_rrule(started_at=plan.first_completed_at)
            recurrence = (
                random.choice(
                    (
                        plan.recurrence,
                        rand_rec,
                    )
                )
                if plan.recurrence
                else rand_rec
            )
            input_plans.append(
                UpdatePlanInput(
                    **plan.model_dump(by_alias=True)
                    | {
                        PlanFields.NAME: random.choice((plan.name, PrimitiveTypesGenerator.generate_random_string())),
                        DocumentLabels.TAGS: [
                            PrimitiveTypesGenerator.generate_random_string()
                            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=3))
                        ],
                        PlanFields.NOTE: PrimitiveTypesGenerator.generate_random_string(),
                        PlanFields.STREAK: PlanStreakBuilder().build(),
                        PlanFields.NEXT_SCHEDULED_AT: PrimitiveTypesGenerator.generate_random_aware_datetime(
                            gte=plan.first_completed_at, lte=plan.next_scheduled_at
                        ),
                        PlanFields.RECURRENCE: str(recurrence),
                    }
                )
            )

        # Act
        response = await _call_patch_endpoint(
            request_url=PlanEndpointUrls.BASE,
            json=json.loads(UpdatePlansAPIRequestInput(documents=input_plans).model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        updated_plans = CommonDocumentsResponse[PlanAPIOutput](**response.json()).documents

        input_plan: UpdatePlanInput
        updated_plan: PlanAPIOutput
        for input_plan, updated_plan in zip(
            sorted(input_plans, key=lambda p: p.id), sorted(updated_plans, key=lambda p: p.id)
        ):
            updated_at = updated_plan.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_plan.type == input_plan.type
            assert updated_plan.name == input_plan.name
            assert updated_plan.recurrence == input_plan.recurrence
            assert updated_plan.next_scheduled_at == input_plan.next_scheduled_at
            assert updated_plan.template_id == input_plan.template_id
            assert updated_plan.priority == input_plan.priority
            assert updated_plan.prompt == input_plan.prompt
            assert updated_plan.note == input_plan.note
            assert updated_plan.tags == input_plan.tags
            assert updated_plan.is_urgent == input_plan.is_urgent
            assert updated_plan.is_confirmation_required == input_plan.is_confirmation_required
            assert updated_plan.is_absolute_schedule == input_plan.is_absolute_schedule
            assert not updated_plan.archived_at

    async def test_update_plan_by_id_when_duplication_fields_do_not_change_should_pass(
        self, user_with_templates_and_plans
    ):
        # Arrange
        existing_plans, user, _ = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        non_archived_plans = [p for p in existing_plans if not p.archived_at]

        input_plans = []
        for plan in non_archived_plans:
            input_plans.append(
                UpdatePlanInput(
                    **plan.model_dump(by_alias=True)
                    | {
                        PlanFields.STREAK: PlanStreakBuilder().build(),
                    }
                )
            )

        # Act
        response = await _call_patch_endpoint(
            request_url=PlanEndpointUrls.BASE,
            json=json.loads(
                UpdatePlansAPIRequestInput(
                    documents=[UpdatePlanInput(**p.model_dump()) for p in input_plans]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        updated_plans = CommonDocumentsResponse[PlanAPIOutput](**response.json()).documents

        input_plan: UpdatePlanInput
        updated_plan: PlanAPIOutput
        for input_plan, updated_plan in zip(
            sorted(input_plans, key=lambda p: p.id), sorted(updated_plans, key=lambda p: p.id)
        ):
            updated_at = updated_plan.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_plan.type == input_plan.type
            assert updated_plan.name == input_plan.name
            assert updated_plan.recurrence == input_plan.recurrence
            assert updated_plan.next_scheduled_at == input_plan.next_scheduled_at
            assert updated_plan.template_id == input_plan.template_id
            assert updated_plan.note == input_plan.note
            assert updated_plan.tags == input_plan.tags
            assert updated_plan.prompt == input_plan.prompt
            assert updated_plan.priority == input_plan.priority
            assert updated_plan.max_completed == input_plan.max_completed
            assert updated_plan.current_completed == input_plan.current_completed
            assert updated_plan.is_urgent == input_plan.is_urgent
            assert updated_plan.is_confirmation_required == input_plan.is_confirmation_required
            assert updated_plan.is_absolute_schedule == input_plan.is_absolute_schedule
            assert not updated_plan.archived_at

    async def test_update_plans_by_id_when_update_makes_duplicate_should_fail(
        self, plan_repo: PlanRepository, user_with_templates_and_plans
    ):
        # Arrange
        _, user, existing_templates = user_with_templates_and_plans
        started_at = datetime.now(timezone.utc)

        plans = [
            PlanBuilder()
            .with_template_id(template_id=random.choice([t.id for t in existing_templates]))
            .with_owner_id(owner_id=user.user_uuid)
            .with_archived_at(archived_at=False)
            .with_rrule_started_at(dt=started_at)
            .with_recurrence(recurrence=PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at))
            .build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=2, max_value=5))
        ]
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        inserted_plans = await plan_repo.insert(plans=plans, force_strong_consistency=True)

        plan_to_update = inserted_plans[0]
        # Switch plan id and update content of one to be duplicate of the other
        updated_plan = UpdatePlanInput.map(
            model=plan_to_update,
            fields={
                PlanFields.NAME: inserted_plans[1].name,
                PlanFields.RECURRENCE: str(inserted_plans[1].recurrence),
                PlanFields.TEMPLATE_ID: inserted_plans[1].template_id,
                PlanFields.NEXT_SCHEDULED_AT: inserted_plans[1].next_scheduled_at,
            },
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=PlanEndpointUrls.BASE,
            json=json.loads(UpdatePlansAPIRequestInput(documents=[updated_plan]).model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response: {response.json()}"
        await plan_repo.delete_by_id(ids=[p.id for p in plans])

    async def test_update_plan_recurrence_to_null_should_pass(
        self, plan_repo: PlanRepository, user_with_templates_and_plans
    ):
        # Arrange
        _, user, existing_templates = user_with_templates_and_plans
        started_at = datetime.now(timezone.utc)

        plans = [
            PlanBuilder()
            .with_template_id(template_id=random.choice([t.id for t in existing_templates]))
            .with_owner_id(owner_id=user.user_uuid)
            .with_archived_at(archived_at=False)
            .with_rrule_started_at(dt=started_at)
            .with_recurrence(recurrence=PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at))
            .build()
        ]
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        inserted_plans = await plan_repo.insert(plans=plans, force_strong_consistency=True)

        plan_to_update = inserted_plans[0]

        updated_plan = UpdatePlanInput.map(
            model=plan_to_update,
            fields={
                PlanFields.RECURRENCE: None,
                PlanFields.MAX_COMPLETED: plan_to_update.current_completed + 1,
            },
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=PlanEndpointUrls.BASE,
            json=json.loads(UpdatePlansAPIRequestInput(documents=[updated_plan]).model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        assert response.json()["documents"][0]["recurrence"] is None
        await plan_repo.delete_by_id(ids=[p.id for p in plans])

    async def test_update_plans_by_id_invalid_recurrence_fails(
        self, plan_repo: PlanRepository, user_with_templates_and_plans
    ):
        # Arrange
        existing_plans, user, _ = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        plan = random.choice([p for p in existing_plans if not p.archived_at]).model_dump()

        plan_with_count = plan | {
            PlanFields.RECURRENCE: "DTSTART:2024-11-25T15:58:00-05:00\nRRULE:FREQ=YEARLY;INTERVAL=0"
        }

        plan_incorrect_interval = plan | {
            PlanFields.RECURRENCE: "DTSTART:2024-11-25T15:58:00-05:00\nRRULE:FREQ=YEARLY;COUNT=1"
        }

        plan_not_tz_aware = plan | {PlanFields.RECURRENCE: "DTSTART:2024-11-25T15:58:00\nRRULE:FREQ=YEARLY"}

        # Act
        for plan in (plan_with_count, plan_incorrect_interval, plan_not_tz_aware):
            response = await _call_patch_endpoint(
                request_url=PlanEndpointUrls.BASE,
                json=json.loads(json.dumps({"documents": [plan]}, default=str)),
                headers=headers,
                retry=False,
            )

            # Assert
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"response: {response.json()}"

    async def test_complete_plans_passes(
        self,
        template_repo: TemplateRepository,
        event_repo: EventRepository,
        user_with_templates_and_plans,
    ):
        # Arrange
        existing_plans, user, existing_templates = user_with_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        non_archived_plans = [p for p in existing_plans if not p.archived_at]
        now = datetime.now(timezone.utc).replace(microsecond=0)

        complete_inputs = [CompletePlanInput(id=p.id, completed_at=now) for p in non_archived_plans]
        request_input = CompletePlansAPIRequestInput(documents=complete_inputs)
        # Act
        response = await _call_post_endpoint(
            request_url=PlanEndpointUrls.COMPLETE,
            json=json.loads(request_input.model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        completed_plans = CommonDocumentsResponse[CompletePlanAPIOutput](**response.json()).documents

        stored_events = await event_repo.search_by_id(
            ids_and_types=[
                (e.id, TypeResolver.get_event(e.type))
                for e in itertools.chain.from_iterable([p.events for p in completed_plans])
            ]
        )

        existing_plan: Plan
        completed_plan: CompletePlanAPIOutput
        for existing_plan, complete_input, completed_plan in zip(
            sorted(non_archived_plans, key=lambda p: p.id),
            sorted(complete_inputs, key=lambda p: p.id),
            sorted(completed_plans, key=lambda p: p.id),
        ):
            assert existing_plan.id == completed_plan.id == complete_input.id
            assert completed_plan.first_completed_at == existing_plan.first_completed_at or now
            assert completed_plan.is_absolute_schedule == existing_plan.is_absolute_schedule
            if existing_plan.is_absolute_schedule:
                if existing_plan.recurrence:
                    assert completed_plan.recurrence == str(existing_plan.recurrence)
                    before_tick = existing_plan.recurrence.before(existing_plan.next_scheduled_at)
                    # too early
                    if not before_tick or complete_input.completed_at < before_tick:
                        assert completed_plan.next_scheduled_at == existing_plan.next_scheduled_at
                    # too late
                    elif complete_input.completed_at > existing_plan.recurrence.after(existing_plan.next_scheduled_at):
                        assert completed_plan.next_scheduled_at == existing_plan.recurrence.after(
                            dt=complete_input.completed_at
                        )
                    else:
                        assert completed_plan.next_scheduled_at == existing_plan.recurrence.after(
                            dt=existing_plan.next_scheduled_at
                        )
                else:
                    assert completed_plan.recurrence is None
                    assert completed_plan.next_scheduled_at == existing_plan.next_scheduled_at
            else:
                rec = (
                    CustomRRule.from_rrule(existing_plan.recurrence.replace(dtstart=now))
                    if existing_plan.recurrence
                    else None
                )
                if rec:
                    assert completed_plan.recurrence == str(rec)
                    assert completed_plan.next_scheduled_at == rec.after(dt=now)
                else:
                    assert completed_plan.recurrence is None
                    assert completed_plan.next_scheduled_at == existing_plan.next_scheduled_at
            if existing_plan.max_completed and completed_plan.current_completed >= existing_plan.max_completed:
                assert completed_plan.archived_at
            else:
                assert not completed_plan.archived_at
            assert completed_plan.template_id == existing_plan.template_id
            assert completed_plan.note == existing_plan.note
            assert completed_plan.tags == existing_plan.tags
            assert completed_plan.prompt == existing_plan.prompt
            assert completed_plan.priority == existing_plan.priority
            assert completed_plan.max_completed == existing_plan.max_completed
            assert completed_plan.current_completed == existing_plan.current_completed + 1
            assert completed_plan.is_urgent == existing_plan.is_urgent
            assert completed_plan.is_confirmation_required == existing_plan.is_confirmation_required
            assert completed_plan.system_properties.created_at == existing_plan.system_properties.created_at

            template = [t for t in existing_templates if t.id == completed_plan.template_id][0]
            assert template

            plan_events = (e for e in stored_events if e.plan_extension.plan_id == completed_plan.id)
            for e in plan_events:
                assert e
                assert e.timestamp == now
                if isinstance(template, EventTemplate):
                    assert e.template_id == e.template_id
                else:
                    assert e.template_id in template.template_ids

    @pytest.fixture
    async def user_with_event_templates_and_plans(
        self,
        plan_repo: PlanRepository,
        template_repo: TemplateRepository,
        user_factory: Callable[[], MemberUser],
    ) -> tuple[Sequence[Plan], MemberUser, Sequence[EventTemplate]]:
        user: MemberUser = await user_factory()
        templates_to_insert = (
            EventTemplateBuilder().with_archived_at(False).with_owner_id(owner_id=user.user_uuid).build_n(n=20)
        )

        inserted_templates = await template_repo.insert(templates=templates_to_insert, force_strong_consistency=True)
        plans_to_insert = [
            PlanBuilder().with_template_id(template_id=t.id).with_owner_id(owner_id=user.user_uuid).build()
            for t in inserted_templates
        ]
        random.choice(plans_to_insert).archived_at = None

        inserted_plans = await plan_repo.insert(plans=plans_to_insert, force_strong_consistency=True)

        yield inserted_plans, user, inserted_templates

        # Teardown
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])
        await plan_repo.delete_by_id(ids=[p.id for p in inserted_plans])

    async def test_complete_plan_with_input_payload_passes(
        self,
        template_repo: TemplateRepository,
        event_repo: EventRepository,
        user_with_event_templates_and_plans,
    ):
        # Arrange
        existing_plans, user, existing_templates = user_with_event_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        existing_plan: Plan = random.choice([p for p in existing_plans if not p.archived_at])
        template: EventTemplate = [t for t in existing_templates if t.id == existing_plan.template_id][0]

        now = datetime.now(timezone.utc).replace(microsecond=0)
        new_template_payload = (
            TypeResolver.get_template_payload_builder(type_id=template.document.type_id())()
            .with_origin(Origin(PrimitiveTypesGenerator.generate_random_enum(InsertableOrigin).value))
            .build()
        )

        complete_input = CompletePlanInput(id=existing_plan.id, completed_at=now, payload=new_template_payload)
        request_input = CompletePlansAPIRequestInput(documents=[complete_input])
        # Act
        response = await _call_post_endpoint(
            request_url=PlanEndpointUrls.COMPLETE,
            json=json.loads(request_input.model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        completed_plan = CommonDocumentsResponse[CompletePlanAPIOutput](**response.json()).documents[0]

        stored_event = (
            await event_repo.search_by_id(
                ids_and_types=[(e.id, TypeResolver.get_event(type_id=e.type)) for e in completed_plan.events]
            )
        )[0]

        # Simplified template asserts
        assert existing_plan.id == completed_plan.id == complete_input.id
        assert completed_plan.first_completed_at == existing_plan.first_completed_at or now
        template = [t for t in existing_templates if t.id == completed_plan.template_id][0]
        assert template

        # Main focus of the test - the generated payload was used instead of the stored one
        assert stored_event
        assert stored_event.timestamp == now
        assert stored_event.duration == new_template_payload.duration
        assert stored_event.name == new_template_payload.name

    async def test_complete_plan_with_missmatch_input_payload_raises_400(
        self,
        template_repo: TemplateRepository,
        event_repo: EventRepository,
        user_with_event_templates_and_plans,
    ):
        # Arrange
        existing_plans, user, existing_templates = user_with_event_templates_and_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        existing_plan: Plan = random.choice([p for p in existing_plans if not p.archived_at])
        template: EventTemplate = [t for t in existing_templates if t.id == existing_plan.template_id][0]

        now = datetime.now(timezone.utc).replace(microsecond=0)

        missmatch_type = random.choice(
            [t for t in EventV3Type if (t.value != template.document.type and t != DataType.EventGroup)]
        )
        new_template_payload = TypeResolver.get_template_payload_builder(type_id=missmatch_type.value)().build()

        complete_input = CompletePlanInput(id=existing_plan.id, completed_at=now, payload=new_template_payload)
        request_input = CompletePlansAPIRequestInput(documents=[complete_input])
        # Act
        response = await _call_post_endpoint(
            request_url=PlanEndpointUrls.COMPLETE,
            json=json.loads(request_input.model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response: {response.json()}"

    @pytest.fixture
    async def user_with_absolute_plan_that_has_not_started(
        self,
        plan_repo: PlanRepository,
        template_repo: TemplateRepository,
        user_factory: Callable[[], MemberUser],
    ) -> tuple[Plan, MemberUser, EventTemplate]:
        user: MemberUser = await user_factory()
        event_template = EventTemplateBuilder().with_archived_at(False).with_owner_id(owner_id=user.user_uuid).build()

        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = CustomRRule(dtstart=started_at, freq=DAILY)

        inserted_templates = await template_repo.insert(templates=[event_template], force_strong_consistency=True)
        plan = (
            PlanBuilder()
            .with_next_scheduled_at(dt=started_at)
            .with_recurrence(recurrence=recurrence)
            .with_archived_at(archived_at=False)
            .with_template_id(template_id=event_template.id)
            .with_owner_id(owner_id=user.user_uuid)
            .with_is_absolute(is_absolute=True)
            .build()
        )

        inserted_plans = await plan_repo.insert(plans=[plan], force_strong_consistency=True)

        yield inserted_plans[0], user, inserted_templates[0]

        # Teardown
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])
        await plan_repo.delete_by_id(ids=[p.id for p in inserted_plans])

    async def test_complete_absolute_non_started_plan_passes(
        self,
        template_repo: TemplateRepository,
        event_repo: EventRepository,
        user_with_absolute_plan_that_has_not_started,
    ):
        # Arrange
        plan, user, template = user_with_absolute_plan_that_has_not_started
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        completed_at = plan.next_scheduled_at - timedelta(days=1, seconds=1)

        complete_inputs = [CompletePlanInput(id=plan.id, completed_at=completed_at)]
        request_input = CompletePlansAPIRequestInput(documents=complete_inputs)
        # Act
        response = await _call_post_endpoint(
            request_url=PlanEndpointUrls.COMPLETE,
            json=json.loads(request_input.model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response: {response.json()}"
        completed_plans = CommonDocumentsResponse[CompletePlanAPIOutput](**response.json()).documents

        completed_plan = completed_plans[0]

        assert completed_plan.current_completed == plan.current_completed + 1
        assert completed_plan.next_scheduled_at == plan.next_scheduled_at
        assert completed_plan.streak.streak == plan.streak.streak
        assert completed_plan.streak.total_triggered == plan.streak.total_triggered + 1
