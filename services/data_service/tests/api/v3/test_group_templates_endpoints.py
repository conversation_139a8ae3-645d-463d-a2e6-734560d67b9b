import json

from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.group_template_builder import GroupTemplateBuilder
from services.data_service.api.urls import TemplateEndpointUrls
from services.data_service.api.v3.models.request.template.insert_template_request_input import (
    InsertTemplateAPIRequestInput,
)
from services.data_service.api.v3.models.response.template.template_api_output import (
    EventTemplateAPIOutput,
    GroupTemplateAPIOutput,
    TemplateAPIOutput,
)
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertGroupTemplateInputBoundaryItem,
    InsertSeparateGroupTemplateInputBoundaryItem,
)
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateGroupTemplateInputBoundaryItem,
)
from services.data_service.tests.api.builders.insert_event_template_request_input_builder import (
    InsertEventTemplateRequestInputBuilder,
)
from services.data_service.tests.api.builders.insert_group_template_request_input_builder import (
    InsertGroupTemplateRequestInputBuilder,
)
from services.data_service.tests.api.builders.insert_separate_group_template_request_input_builder import (
    InsertSeparateGroupTemplateRequestInputBuilder,
    InsertSeparateGroupTemplateRequestInputItemBuilder,
)
from services.data_service.tests.api.builders.search_templates_request_input_builder import (
    SearchTemplatesRequestInputBuilder,
)
from services.data_service.tests.api.builders.update_event_template_request_input_builder import (
    UpdateEventTemplateRequestInputBuilder,
)
from services.data_service.tests.api.builders.update_group_template_request_input_builder import (
    UpdateGroupTemplateRequestInputItemBuilder,
)
from services.data_service.tests.api.common_rpc_calls import (
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.tests.api.utils.test_utils import TestUtils


class TestGroupTemplate:
    async def test_search_group_templates_endpoint_should_pass(
        self, template_repo: TemplateRepository, user_headers_factory
    ):
        # Arrange
        user, headers = await user_headers_factory()
        templates_to_insert = []
        for i in range(PrimitiveTypesGenerator.generate_random_int(2, 5)):
            event_templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
            templates_to_insert.extend(event_templates)
            templates_to_insert.append(
                GroupTemplateBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_template_ids([t.id for t in event_templates])
                .build()
            )

        inserted_templates = await template_repo.insert(templates=templates_to_insert, force_strong_consistency=True)

        request_builder = SearchTemplatesRequestInputBuilder().with_limit(len(inserted_templates))

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_params_as_dict(),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        fetched_templates = payload.documents
        assert len(fetched_templates) == len(inserted_templates)
        sorted_inserted_templates = sorted(
            inserted_templates, key=lambda t: (t.system_properties.created_at, t.id), reverse=True
        )

        # Expecting all EventTemplates
        for template, expected_template in zip(fetched_templates, sorted_inserted_templates):
            assert template.id == expected_template.id
            assert template.name == expected_template.name
            assert template.tags == expected_template.tags
            if isinstance(template, EventTemplateAPIOutput):
                assert isinstance(expected_template, EventTemplate)
                assert template.document_name == expected_template.document_name
                assert template.document.type == expected_template.document.type
                assert template.document.tags == expected_template.document.tags
                assert template.document.duration == expected_template.document.duration
            elif isinstance(template, GroupTemplateAPIOutput):
                assert isinstance(expected_template, GroupTemplate)
                assert template.template_ids == expected_template.template_ids
            else:
                raise ValueError(
                    f"Unexpected type for search group templates: {type(template)}, expected_template: {type(expected_template)}"
                )
            assert template.system_properties.created_at == expected_template.system_properties.created_at
            assert template.system_properties.updated_at == expected_template.system_properties.updated_at
            assert template.system_properties.deleted_at == expected_template.system_properties.deleted_at

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_search_group_templates_endpoint_no_templates_should_return_204(
        self,
        user_headers_factory,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        request_builder = SearchTemplatesRequestInputBuilder()

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_params_as_dict(),
            headers=headers,
            retry=False,
        )
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT, response.content

    async def test_insert_separate_group_template_endpoint_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        request_input = InsertEventTemplateRequestInputBuilder().build()

        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        inserted_templates = payload.documents
        inserted_template_ids = [template.id for template in inserted_templates]

        group_insert_input = (
            InsertSeparateGroupTemplateRequestInputItemBuilder().with_template_ids(inserted_template_ids).build()
        )
        request_input = InsertTemplateAPIRequestInput(documents=[group_insert_input])

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        inserted_templates = payload.documents
        input_templates = request_input.documents

        assert len(inserted_templates) == len(input_templates)

        for inserted_template, expected_template in zip(inserted_templates, input_templates):
            assert isinstance(inserted_template, GroupTemplateAPIOutput)
            assert isinstance(expected_template, InsertSeparateGroupTemplateInputBoundaryItem)
            assert inserted_template.name == expected_template.name
            assert inserted_template.tags == expected_template.tags
            assert inserted_template.template_ids == expected_template.template_ids

            assert inserted_template.system_properties.created_at
            assert TestUtils.is_date_within_one_minute(inserted_template.system_properties.created_at)
            assert inserted_template.system_properties.updated_at is None
            assert inserted_template.system_properties.deleted_at is None

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates] + inserted_template_ids)

    async def test_insert_group_template_endpoint_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()

        request_input = InsertGroupTemplateRequestInputBuilder().build()

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        input_templates = request_input.documents
        inserted_group_templates = []
        for document in payload.documents:
            if isinstance(document, GroupTemplateAPIOutput):
                inserted_group_templates.append(document)

        for inserted_template, expected_template in zip(inserted_group_templates, input_templates):
            assert isinstance(inserted_template, GroupTemplateAPIOutput)
            assert isinstance(expected_template, InsertGroupTemplateInputBoundaryItem)
            assert inserted_template.name == expected_template.name
            assert inserted_template.tags == expected_template.tags
            assert len(inserted_template.template_ids) == len(expected_template.templates)

            assert inserted_template.system_properties.created_at
            assert TestUtils.is_date_within_one_minute(inserted_template.system_properties.created_at)
            assert inserted_template.system_properties.updated_at is None
            assert inserted_template.system_properties.deleted_at is None

        # Teardown
        await template_repo.delete_by_id([t.id for t in payload.documents])

    async def test_insert_separate_group_template_endpoint_should_raise_bad_request_template_ids_dont_exist(
        self,
        user_headers_factory,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        request_input = InsertSeparateGroupTemplateRequestInputBuilder().build()

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        # Template ids don't exist
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

    async def test_insert_group_template_in_group_template_should_raise(
        self, user_headers_factory, template_repo: TemplateRepository
    ):
        user, headers = await user_headers_factory()
        event_templates = list(EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n())
        sub_group_template = GroupTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build()
        event_templates_to_create = []
        for event_template_id in sub_group_template.template_ids:
            event_templates_to_create.append(
                EventTemplateBuilder().with_id(event_template_id).with_owner_id(user.user_uuid).build()
            )
        templates_to_insert = event_templates + [sub_group_template] + event_templates_to_create
        inserted_templates = await template_repo.insert(templates=templates_to_insert, force_strong_consistency=True)
        template_ids = [template.id for template in event_templates] + [sub_group_template.id]
        group_insert_input = (
            InsertSeparateGroupTemplateRequestInputItemBuilder().with_template_ids(template_ids).build()
        )
        request_input = InsertTemplateAPIRequestInput(documents=[group_insert_input])

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_update_group_template_endpoint_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        event_templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
        group_template = (
            GroupTemplateBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_template_ids([t.id for t in event_templates])
            .build()
        )
        new_event_template = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build()

        inserted_templates = await template_repo.insert(
            templates=list(event_templates) + [group_template] + [new_event_template], force_strong_consistency=True
        )

        # Removes the last template_ids in the inserted group and adds a new one
        updated_templates = []
        for inserted_template in inserted_templates:
            if isinstance(inserted_template, GroupTemplate):
                new_template_ids = list(inserted_template.template_ids[:-1])
                new_template_ids = new_template_ids + [new_event_template.id]

                updated_templates.append(
                    UpdateGroupTemplateRequestInputItemBuilder()
                    .with_id(inserted_template.id)
                    .with_template_ids(template_ids=new_template_ids)
                    .build()
                )

        request_input = UpdateEventTemplateRequestInputBuilder().with_values(values=updated_templates).build()

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        returned_templates = payload.documents

        assert len(returned_templates) == len(updated_templates)

        for updated_template, expected_template in zip(
            sorted(returned_templates, key=lambda p: p.id), sorted(updated_templates, key=lambda p: p.id)
        ):
            assert isinstance(updated_template, GroupTemplateAPIOutput)
            assert isinstance(expected_template, UpdateGroupTemplateInputBoundaryItem)
            assert updated_template.name == expected_template.name
            assert updated_template.tags == expected_template.tags
            assert updated_template.template_ids == expected_template.template_ids
            assert updated_template.system_properties.created_at
            assert updated_template.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_template.system_properties.updated_at)
            assert updated_template.system_properties.deleted_at is None

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_update_group_template_endpoint_with_template_that_does_not_exist_should_raise(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        event_templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
        group_template = (
            GroupTemplateBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_template_ids([t.id for t in event_templates])
            .build()
        )

        inserted_templates = await template_repo.insert(
            templates=list(event_templates) + [group_template],
            force_strong_consistency=True,
        )

        new_event_template = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build()
        updated_templates = []
        for inserted_template in inserted_templates:
            if isinstance(inserted_template, GroupTemplate):
                new_template_ids = list(inserted_template.template_ids[:-1])
                new_template_ids = new_template_ids + [new_event_template.id]

                updated_templates.append(
                    UpdateGroupTemplateRequestInputItemBuilder()
                    .with_id(inserted_template.id)
                    .with_template_ids(template_ids=new_template_ids)
                    .build()
                )

        request_input = UpdateEventTemplateRequestInputBuilder().with_values(values=updated_templates).build()

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_update_group_template_endpoint_with_group_template_id_should_raise(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        event_templates1 = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
        group_template1 = (
            GroupTemplateBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_template_ids([t.id for t in event_templates1])
            .build()
        )
        event_templates2 = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
        group_template2 = (
            GroupTemplateBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_template_ids([t.id for t in event_templates2])
            .build()
        )

        inserted_templates = await template_repo.insert(
            templates=list(event_templates1) + list(event_templates2) + [group_template1] + [group_template2],
            force_strong_consistency=True,
        )

        updated_templates = []

        new_template_ids = list(group_template1.template_ids[:-1])
        new_template_ids = new_template_ids + [group_template2.id]

        updated_templates.append(
            UpdateGroupTemplateRequestInputItemBuilder()
            .with_id(group_template1.id)
            .with_template_ids(template_ids=new_template_ids)
            .build()
        )

        request_input = UpdateEventTemplateRequestInputBuilder().with_values(values=updated_templates).build()

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_archive_group_templates_endpoint_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        templates_to_insert = []
        not_archived_template = GroupTemplateBuilder().with_owner_id(user.user_uuid).with_archived_at(False).build()
        templates_to_insert.append(not_archived_template)
        for event_template_id in not_archived_template.template_ids:
            event_template = (
                EventTemplateBuilder().with_id(id=event_template_id).with_owner_id(owner_id=user.user_uuid).build()
            )
            templates_to_insert.append(event_template)
        for i in range(PrimitiveTypesGenerator.generate_random_int(2, 5)):
            event_templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
            templates_to_insert.extend(event_templates)
            templates_to_insert.append(
                GroupTemplateBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_template_ids([t.id for t in event_templates])
                .build()
            )

        inserted_templates = await template_repo.insert(templates=templates_to_insert, force_strong_consistency=True)

        # Arrange
        doc_ids = [t.id for t in inserted_templates if not t.archived_at and isinstance(t, GroupTemplate)]
        request_url = join_as_url(base_url=TemplateEndpointUrls.ARCHIVE, query_params={"template_ids": doc_ids})

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content

        documents = await template_repo.search_by_id(ids=doc_ids)
        assert all([bool(d.archived_at) for d in documents])

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_archive_group_templates_endpoint_archiving_archived_template_should_raise(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        templates_to_insert = []
        archived_template = (
            GroupTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_archived_at(PrimitiveTypesGenerator.generate_random_aware_datetime())
            .build()
        )
        templates_to_insert.append(archived_template)
        for event_template_id in archived_template.template_ids:
            event_template = (
                EventTemplateBuilder().with_id(id=event_template_id).with_owner_id(owner_id=user.user_uuid).build()
            )
            templates_to_insert.append(event_template)

        inserted_templates = await template_repo.insert(templates=templates_to_insert, force_strong_consistency=True)

        # Arrange
        doc_ids = [t.id for t in inserted_templates if isinstance(t, GroupTemplate)]
        request_url = join_as_url(base_url=TemplateEndpointUrls.ARCHIVE, query_params={"template_ids": doc_ids})

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])
