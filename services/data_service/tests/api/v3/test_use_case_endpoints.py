import json
from typing import Callable, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.domain.schemas.events.use_case import UseCase, UseCaseFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.use_case_builder import UseCaseBuilder
from services.data_service.api.urls import UseCaseEndpointUrls
from services.data_service.api.v3.models.request.use_case.insert_use_case_api_request_input import (
    InsertUseCaseAPIRequestInput,
)
from services.data_service.api.v3.models.request.use_case.update_use_case_api_request_input import (
    UpdateUseCaseAPIRequestInput,
)
from services.data_service.api.v3.models.response.use_case.use_case_api_output import UseCaseAPIOutput
from services.data_service.application.use_cases.use_case.models.insert_use_case_input_boundary import (
    InsertUseCaseInput,
)
from services.data_service.application.use_cases.use_case.models.update_use_case_input_boundary import (
    UpdateUseCaseInput,
)
from services.data_service.tests.api.builders.insert_use_case_api_request_input_builder import (
    InsertUseCaseAPIRequestInputBuilder,
)
from services.data_service.tests.api.builders.insert_use_case_input_builder import InsertUseCaseInputBuilder
from services.data_service.tests.api.common_rpc_calls import (
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.tests.api.utils.test_utils import TestUtils


class TestUseCaseCRUD:
    @pytest.fixture
    async def user_with_use_cases(
        self,
        use_case_repo: UseCaseRepository,
        user_factory: Callable[[], MemberUser],
    ) -> tuple[Sequence[UseCase], MemberUser]:
        user: MemberUser = await user_factory()
        use_cases = [
            UseCaseBuilder().with_owner_id(owner_id=user.user_uuid).build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
        ]
        inserted_use_cases = await use_case_repo.insert(use_cases=use_cases, force_strong_consistency=True)

        yield inserted_use_cases, user

        # Teardown
        await use_case_repo.delete_by_id(ids=[p.id for p in inserted_use_cases])

    async def test_insert_use_cases_should_pass(self, user_with_use_cases, use_case_repo: UseCaseRepository):
        # Arrange
        existing_use_cases, user = user_with_use_cases
        load_use_case_request = (
            InsertUseCaseAPIRequestInputBuilder()
            .with_use_cases(use_cases=[InsertUseCaseInputBuilder().build() for _ in range(1, 10)])
            .build()
        )
        body_dict = json.loads(load_use_case_request.model_dump_json(by_alias=True))
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=UseCaseEndpointUrls.BASE, json=body_dict, headers=headers, retry=False
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        response_model = CommonDocumentsResponse[UseCaseAPIOutput](**response.json())
        assert len(response_model.documents) == len(load_use_case_request.documents)

        for use_case, expected_use_case in zip(
            sorted(response_model.documents, key=lambda p: p.name),
            sorted(load_use_case_request.documents, key=lambda p: p.name),
        ):
            assert use_case.name == expected_use_case.name
            assert use_case.tags == expected_use_case.tags
            assert use_case.archived_at is None
            assert TestUtils.is_date_within_one_minute(use_case.system_properties.created_at)

        # Teardown
        await use_case_repo.delete_by_id([p.id for p in response_model.documents])

    async def test_insert_duplicated_use_cases_should_raise_bad_request(self, user_with_use_cases):
        # Arrange
        existing_use_cases, user = user_with_use_cases
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=UseCaseEndpointUrls.BASE,
            json=json.loads(
                InsertUseCaseAPIRequestInput(
                    documents=[InsertUseCaseInput(**p.model_dump()) for p in existing_use_cases]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response content: {response.content}"

    async def test_archive_use_cases_should_pass(self, user_with_use_cases):
        # Arrange
        existing_use_cases, user = user_with_use_cases
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            base_url=UseCaseEndpointUrls.ARCHIVE, query_params={"use_case_ids": [p.id for p in existing_use_cases]}
        )

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        archived_use_cases = CommonDocumentsResponse[UseCaseAPIOutput](**response.json()).documents
        for use_case in archived_use_cases:
            archived_at = use_case.archived_at
            assert archived_at
            assert TestUtils.is_date_within_one_minute(date=archived_at)

    async def test_search_use_cases_endpoint_should_pass(self, user_with_use_cases):
        # Arrange
        existing_use_cases, user = user_with_use_cases
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=UseCaseEndpointUrls.SEARCH,
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        actual_use_cases = CommonDocumentsResponse[UseCaseAPIOutput](**response.json()).documents
        assert len(actual_use_cases) == len(existing_use_cases)

        expected_use_case: UseCase
        use_case: UseCaseAPIOutput
        for use_case, expected_use_case in zip(
            sorted(actual_use_cases, key=lambda p: p.id), sorted(existing_use_cases, key=lambda p: p.id)
        ):
            assert use_case.id == expected_use_case.id
            assert use_case.name == expected_use_case.name
            assert use_case.tags == expected_use_case.tags
            assert use_case.system_properties.created_at == expected_use_case.system_properties.created_at
            assert use_case.archived_at == expected_use_case.archived_at

    async def test_update_use_cases_by_id_should_pass(self, user_with_use_cases):
        # Arrange
        existing_use_cases, user = user_with_use_cases
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        input_use_cases = []

        for use_case in existing_use_cases:
            input_use_case = UseCase(
                **use_case.model_dump(by_alias=True)
                | {
                    UseCaseFields.NAME: PrimitiveTypesGenerator.generate_random_string(),
                    DocumentLabels.TAGS: [
                        PrimitiveTypesGenerator.generate_random_string()
                        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=3))
                    ],
                }
            )

            input_use_cases.append(input_use_case)

        # Act
        response = await _call_patch_endpoint(
            request_url=UseCaseEndpointUrls.BASE,
            json=json.loads(
                UpdateUseCaseAPIRequestInput(
                    documents=[UpdateUseCaseInput(**p.model_dump()) for p in input_use_cases]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        updated_use_cases = CommonDocumentsResponse[UseCaseAPIOutput](**response.json()).documents

        input_use_case: UseCase
        updated_use_case: UseCaseAPIOutput
        for input_use_case, updated_use_case in zip(
            sorted(input_use_cases, key=lambda p: p.id), sorted(updated_use_cases, key=lambda p: p.id)
        ):
            updated_at = updated_use_case.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_use_case.name == input_use_case.name
            assert updated_use_case.tags == input_use_case.tags
            assert updated_use_case.system_properties.created_at == input_use_case.system_properties.created_at
            assert updated_use_case.archived_at == input_use_case.archived_at

    async def test_update_use_cases_by_id_when_duplication_fields_do_not_change_should_pass(self, user_with_use_cases):
        # Arrange
        existing_use_cases, user = user_with_use_cases
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        input_use_cases = []

        for use_case in existing_use_cases:
            input_use_case = UseCase(**use_case.model_dump(by_alias=True))

            input_use_cases.append(input_use_case)

        # Act
        response = await _call_patch_endpoint(
            request_url=UseCaseEndpointUrls.BASE,
            json=json.loads(
                UpdateUseCaseAPIRequestInput(
                    documents=[UpdateUseCaseInput(**p.model_dump()) for p in input_use_cases]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        updated_use_cases = CommonDocumentsResponse[UseCaseAPIOutput](**response.json()).documents

        input_use_case: UseCase
        updated_use_case: UseCaseAPIOutput
        for input_use_case, updated_use_case in zip(
            sorted(input_use_cases, key=lambda p: p.id), sorted(updated_use_cases, key=lambda p: p.id)
        ):
            updated_at = updated_use_case.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_use_case.name == input_use_case.name
            assert updated_use_case.tags == input_use_case.tags
            assert updated_use_case.system_properties.created_at == input_use_case.system_properties.created_at
            assert updated_use_case.archived_at == input_use_case.archived_at

    async def test_update_use_cases_by_id_when_update_makes_duplicate_should_fail(
        self, use_case_repo: UseCaseRepository, user_with_use_cases
    ):
        # Arrange
        existing_use_cases, user = user_with_use_cases

        use_cases = UseCaseBuilder().with_owner_id(owner_id=user.user_uuid).build_n(n=2)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        inserted_use_cases = await use_case_repo.insert(use_cases=use_cases, force_strong_consistency=True)

        # Switch use_case id and update content of one to be duplicate of the other
        updated_use_case = UpdateUseCaseInput.map(
            model=inserted_use_cases[0], fields={UseCaseFields.ID: inserted_use_cases[1].id}
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=UseCaseEndpointUrls.BASE,
            json=json.loads(UpdateUseCaseAPIRequestInput(documents=[updated_use_case]).model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response content: {response.content}"
