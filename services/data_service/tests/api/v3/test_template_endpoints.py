import json
import random
from typing import Any, As<PERSON><PERSON>enerator, Sequence, Tuple

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.utils.urls import join_as_url
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template
from services.base.tests.api.query.leaf_query_api_helper import <PERSON><PERSON>ueryAP<PERSON>Helper
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.group_template_builder import GroupTemplateBuilder
from services.data_service.api.enums.template_type import TemplateType
from services.data_service.api.queries.template_query_api import TemplateQueryAPI, TemplateTyped<PERSON>ueryAPI
from services.data_service.api.urls import (
    TemplateEndpointUrls,
)
from services.data_service.api.v3.models.response.template.template_api_output import (
    EventTemplateAPIOutput,
    GroupTemplateAPIOutput,
    TemplateAPIOutput,
)
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateEventTemplateInputBoundaryItem,
    UpdateGroupTemplateInputBoundaryItem,
)
from services.data_service.tests.api.builders.search_templates_request_input_builder import (
    SearchTemplatesRequestInputBuilder,
)
from services.data_service.tests.api.builders.update_event_template_request_input_builder import (
    UpdateEventTemplateRequestInputBuilder,
    UpdateEventTemplateRequestInputItemBuilder,
)
from services.data_service.tests.api.builders.update_group_template_request_input_builder import (
    UpdateGroupTemplateRequestInputItemBuilder,
)
from services.data_service.tests.api.common_rpc_calls import (
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.tests.api.utils.test_utils import TestUtils
from settings.app_constants import DEMO1_UUID


class TestTemplate:

    @pytest.fixture
    async def event_and_group_templates(
        self,
        template_repo: TemplateRepository,
    ) -> AsyncGenerator[Sequence[Template], Any]:
        user = MemberUserBuilder().with_uuid(DEMO1_UUID).build()
        template_name = "test"
        event_template = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).with_name(template_name).build()
        group_template = GroupTemplateBuilder().with_owner_id(owner_id=user.user_uuid).with_name(template_name).build()

        inserted_templates: Sequence[Template] = await template_repo.insert(
            templates=[event_template, group_template],
            force_strong_consistency=True,
        )
        yield inserted_templates

        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    @pytest.fixture
    async def templates_and_user(
        self, template_repo: TemplateRepository, user_headers_factory
    ) -> AsyncGenerator[Tuple[MemberUser, Sequence[Template]], Any]:
        user, _ = await user_headers_factory()

        event_templates = list(EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n())
        group_templates = GroupTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
        event_templates_to_create = []  # Need to create templates for the groups
        for group_template in group_templates:
            for event_template_id in group_template.template_ids:
                event_templates_to_create.append(
                    EventTemplateBuilder().with_id(event_template_id).with_owner_id(user.user_uuid).build()
                )
        # Ensure some templates are non archived
        random.choice(event_templates).archived_at = None
        random.choice(group_templates).archived_at = None
        templates_to_insert = event_templates + group_templates + event_templates_to_create

        inserted_templates = await template_repo.insert(templates=templates_to_insert, force_strong_consistency=True)
        yield user, inserted_templates

        # Teardown
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    async def test_search_both_types_of_templates_endpoint_should_pass(
        self, templates_and_user: Tuple[MemberUser, list[Template]]
    ):
        # Arrange
        user, inserted_templates = templates_and_user
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        request_builder = SearchTemplatesRequestInputBuilder().with_limit(len(inserted_templates))

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_params_as_dict(),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        fetched_templates = payload.documents
        inserted_templates = [t for t in inserted_templates if not t.system_properties.deleted_at]
        sorted_inserted_templates = sorted(
            inserted_templates, key=lambda x: (x.system_properties.created_at, x.id), reverse=True
        )
        assert len(fetched_templates) == len(sorted_inserted_templates)

        for template, expected_template in zip(fetched_templates, sorted_inserted_templates):
            assert template.id == expected_template.id
            assert template.name == expected_template.name
            assert template.tags == expected_template.tags
            if isinstance(template, EventTemplateAPIOutput) and isinstance(expected_template, EventTemplate):
                assert template.document_name == expected_template.document_name
                assert template.document.type == expected_template.document.type
                assert template.document.duration == expected_template.document.duration
            elif isinstance(template, GroupTemplateAPIOutput) and isinstance(expected_template, GroupTemplate):
                assert template.template_ids == expected_template.template_ids
            else:
                raise ValueError(
                    f"unexpected type for template: {type(template)}, expected_template: {type(expected_template)}"
                )
            assert template.system_properties.created_at == expected_template.system_properties.created_at
            assert template.system_properties.updated_at == expected_template.system_properties.updated_at
            assert template.system_properties.deleted_at == expected_template.system_properties.deleted_at

    async def test_archive_both_types_of_templates_endpoint_should_pass(
        self,
        templates_and_user,
    ):
        # Arrange
        user, inserted_templates = templates_and_user
        existing_non_archived_templates = [t for t in inserted_templates if not t.archived_at]
        doc_ids = [t.id for t in existing_non_archived_templates if not t.archived_at]
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(base_url=TemplateEndpointUrls.ARCHIVE, query_params={"template_ids": doc_ids})

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        archived_templates = payload.documents

        assert len(archived_templates) == len(existing_non_archived_templates)
        for existing_template, archived_template in zip(
            (sorted(existing_non_archived_templates, key=lambda t: t.id)),
            (sorted(archived_templates, key=lambda t: t.id)),
        ):
            assert existing_template.id == archived_template.id
            assert not existing_template.archived_at
            assert archived_template.archived_at
            assert TestUtils.is_date_within_one_minute(date=archived_template.archived_at)

    async def test_update_both_types_of_template_endpoint_should_pass(
        self,
        templates_and_user,
    ):
        # Arrange
        user, inserted_templates = templates_and_user
        non_archived_templates = [t for t in inserted_templates if not t.archived_at]
        expected_templates: Sequence[UpdateEventTemplateInputBoundaryItem | UpdateGroupTemplateInputBoundaryItem] = []
        for inserted_template in non_archived_templates:
            if isinstance(inserted_template, EventTemplate):
                expected_templates.append(
                    UpdateEventTemplateRequestInputItemBuilder()
                    .with_document(document=inserted_template.document)
                    .with_id(inserted_template.id)
                    .build()
                )
            elif isinstance(inserted_template, GroupTemplate):
                expected_templates.append(
                    UpdateGroupTemplateRequestInputItemBuilder()
                    .with_id(inserted_template.id)
                    .with_template_ids(
                        template_ids=[t.id for t in non_archived_templates if isinstance(t, EventTemplate)]
                    )
                    .build()
                )

        request_input = UpdateEventTemplateRequestInputBuilder().with_values(values=expected_templates).build()
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        updated_templates = payload.documents

        assert len(updated_templates) == len(expected_templates)

        for updated_template, expected_template in zip(
            sorted(updated_templates, key=lambda p: p.id), sorted(expected_templates, key=lambda p: p.id)
        ):
            assert updated_template.id == expected_template.id
            assert updated_template.name == expected_template.name
            assert updated_template.tags == expected_template.tags
            if isinstance(updated_template, EventTemplateAPIOutput) and isinstance(
                expected_template, UpdateEventTemplateInputBoundaryItem
            ):
                assert updated_template.document_name == expected_template.document.name
                assert updated_template.document.type == expected_template.document.type
                assert updated_template.document.tags == expected_template.document.tags
                assert updated_template.document.duration == expected_template.document.duration
            elif isinstance(updated_template, GroupTemplateAPIOutput) and isinstance(
                expected_template, UpdateGroupTemplateInputBoundaryItem
            ):
                assert updated_template.template_ids == expected_template.template_ids
            else:
                raise ValueError(
                    f"unexpected type for updated template: {type(updated_template)}, expected_template: {type(expected_template)}"
                )
            assert updated_template.system_properties.created_at
            assert updated_template.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_template.system_properties.updated_at)
            assert updated_template.system_properties.deleted_at is None

    async def test_feed_endpoint_returns_type_from_shared_index_should_pass(
        self,
        seed_user_header: dict,
        event_and_group_templates: Sequence[Template],
    ):
        request_builder = SearchTemplatesRequestInputBuilder().with_query(
            query=TemplateQueryAPI(
                queries=[
                    TemplateTypedQueryAPI(
                        types=[TemplateType.EVENT_TEMPLATE],
                        query=LeafQueryAPIHelper.name_values_query(names=[event_and_group_templates[0].name]),
                    )
                ]
            )
        )

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_params_as_dict(),
            headers=seed_user_header,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())

        assert len(payload.documents) == 1

        template = payload.documents[0]
        assert isinstance(template, EventTemplateAPIOutput)

        assert template.name == event_and_group_templates[0].name
        assert template.tags == event_and_group_templates[0].tags
