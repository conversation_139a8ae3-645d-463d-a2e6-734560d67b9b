from __future__ import annotations

from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.domain.annotated_types import NonEmptyStr
from services.data_service.api.queries.event_query_api import EventQueryAPI


class EventFeedAPIRequestInputBuilder:

    def __init__(self):
        self._limit: int | None = None
        self._query: EventQueryAPI | None = None
        self._sort: SortRequestInput | None = None
        self._continuation_token: NonEmptyStr | None = None

    def with_limit(self, limit: int) -> EventFeedAPIRequestInputBuilder:
        self._limit = limit
        return self

    def with_query(self, query: EventQueryAPI) -> EventFeedAPIRequestInputBuilder:
        self._query = query
        return self

    def with_sort(self, sort: SortRequestInput) -> EventFeedAPIRequestInputBuilder:
        self._sort = sort
        return self

    def with_continuation_token(self, continuation_token: NonEmptyStr) -> EventFeedAPIRequestInputBuilder:
        self._continuation_token = continuation_token
        return self

    def build_body_as_dict(self) -> dict:
        query_as_dict = {"queries": self._query.model_dump(mode="json")["queries"]} if self._query else {}
        sort_as_dict = {"sort": self._sort.model_dump(mode="json")} if self._sort else {}

        return query_as_dict | sort_as_dict

    def build_query_params_as_dict(self) -> dict:
        limit = {"limit": self._limit} if self._limit else {}
        continuation_token = {"continuation_token": self._continuation_token} if self._continuation_token else {}

        return limit | continuation_token
