from typing import Any, AsyncGenerator, Awaitable, Callable, Dict

import pytest
from starlette import status

from services.base.domain.schemas.member_user.member_user import MemberUser
from services.data_service.api.urls import AnalyseEndpointUrls
from services.data_service.application.use_cases.trend_detection_use_case import TrendResult
from services.data_service.tests.api.common_rpc_calls import _call_post_endpoint


@pytest.mark.asyncio
class TestTrendDetectionEndpoint:
    """
    Tests for the trend detection POST endpoint using parameterization.
    Focuses on request/response format, status codes, and input validation.
    """

    @pytest.fixture
    async def test_user(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[Any, dict]:
        user, headers = await user_headers_factory()
        yield headers

    passing_test_cases = [
        pytest.param(
            {
                "data_series": [1000, 1010, 1020, 1030, 1040, 1050],
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            TrendResult.UPWARD_TREND,
            id="clear_upward_trend",
        ),
        pytest.param(
            {
                "data_series": [1000, 950, 900, 850, 800, 750],
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            TrendResult.DOWNWARD_TREND,
            id="clear_downward_trend",
        ),
        pytest.param(
            {
                "data_series": [500, 500, 500, 500, 500],
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            TrendResult.NO_TREND,
            id="flat_line_no_trend",
        ),
        pytest.param(
            {
                "data_series": [100, 102, 99, 101, 98, 103, 97, 102],
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            TrendResult.NO_TREND,
            id="oscillating_values_no_trend",
        ),
        pytest.param(
            {
                "data_series": [1000, 1001, 1002, 1003, 1004, 1005],
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            TrendResult.NO_TREND,
            id="slight_increase_below_threshold",
        ),
        pytest.param(
            {
                "data_series": [1000, 1010, 1020, 1030, 1040, 1050],
            },
            TrendResult.UPWARD_TREND,
            id="default_thresholds",
        ),
    ]

    @pytest.mark.parametrize("request_payload, expected_trend", passing_test_cases)
    async def test_trend_detection_endpoint_passes(
        self,
        test_user: dict,
        request_payload: Dict[str, Any],
        expected_trend: TrendResult,
    ):
        headers = test_user
        response = await _call_post_endpoint(
            request_url=AnalyseEndpointUrls.TREND_DETECT,
            json=request_payload,
            headers=headers,
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["trend_result"] == expected_trend.value
        assert "coeficient" in response_data
        assert "intercept" in response_data
        assert isinstance(response_data["coeficient"], (float, type(None)))
        assert isinstance(response_data["intercept"], (float, type(None)))

    failing_test_cases = [
        pytest.param(
            {
                "data_series": [],
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            id="empty_data_series",
        ),
        pytest.param(
            {
                "data_series": [1000],
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            id="single_value_data_series",
        ),
        pytest.param(
            {
                # "data_series": [1, 2, 3], # Missing
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            id="missing_data_series",
        ),
        pytest.param(
            {
                "data_series": "not a list of numbers",  # Invalid type
                "relative_slope_threshold": 0.01,
                "r2_threshold": 0.3,
            },
            id="invalid_data_series_type",
        ),
    ]

    @pytest.mark.parametrize("request_payload", failing_test_cases)
    async def test_trend_detection_endpoint_fails(
        self,
        test_user: dict,
        request_payload: Dict[str, Any],
    ):
        headers = test_user
        response = await _call_post_endpoint(
            request_url=AnalyseEndpointUrls.TREND_DETECT,
            json=request_payload,
            headers=headers,
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
