import json
from typing import Awaitable, Callable, Sequence
from uuid import uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.event_group_builder import EventGroupBuilder
from services.base.type_resolver import TypeResolver
from services.data_service.api.output_models.event_api_output_v3 import EventV3APIOutput
from services.data_service.api.urls import EventEndpointUrls
from services.data_service.api.v3.models.request.event.update_event_api_request_input import UpdateEventAPIRequestInput
from services.data_service.tests.api.common_rpc_calls import _call_patch_endpoint
from services.data_service.tests.api.v3.events.event_assert_helpers import EventAssertHelpers
from services.data_service.tests.application.builders.v3.updatable_event_input_builder import UpdatableEventInputBuilder
from services.data_service.tests.application.builders.v3.update_event_group_input_builder import (
    UpdateEventGroupInputBuilder,
)


class TestUpdateEventEndpoints:
    @pytest.fixture
    async def user_with_events(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[Sequence[Event], MemberUser]:
        user: MemberUser = await user_factory()
        events = (
            EventBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_origin(origin=PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build_all(n=1)
        )

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in inserted_events])

    async def test_update_event_endpoint_passes(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        events_to_update = [
            UpdatableEventInputBuilder().with_id(id=e.id).with_type_id(type_id=e.type_id()).build()
            for e in existing_events
        ]

        request_input = UpdateEventAPIRequestInput(documents=events_to_update)

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        updated_events = payload.documents

        EventAssertHelpers.assert_events(
            returned_events=sorted(payload.documents, key=lambda d: d.id),
            expected_events=sorted(request_input.documents, key=lambda d: d.id),
        )

        for expected_event, updated_event in zip(
            sorted(existing_events, key=lambda e: e.id), sorted(payload.documents, key=lambda d: d.id)
        ):
            assert expected_event.metadata.origin.value == updated_event.metadata.origin.value
            assert expected_event.metadata.source_service == updated_event.metadata.source_service

        # Teardown
        await event_repo.delete_by_id([(e.id, TypeResolver.get_event(e.type)) for e in updated_events])

    async def test_update_event_endpoint_not_found_raises_bad_request(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        events_to_update = [
            UpdatableEventInputBuilder().with_id(id=e.id).with_type_id(type_id=e.type_id()).build()
            for e in existing_events
        ]
        # change single id
        events_to_update[0].id = uuid4()

        request_input = UpdateEventAPIRequestInput(documents=events_to_update)

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.json()

    async def test_update_event_endpoint_different_owner_raises_forbidden(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        events_to_update = [
            UpdatableEventInputBuilder().with_id(id=e.id).with_type_id(type_id=e.type_id()).build()
            for e in existing_events
        ]

        request_input = UpdateEventAPIRequestInput(documents=events_to_update)

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN, response.json()

    @pytest.fixture()
    async def user_with_events_with_end_time(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[Sequence[Event], MemberUser]:
        user: MemberUser = await user_factory()
        time_interval = CustomModelsGenerator.generate_random_time_interval()

        events = (
            EventBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_origin(origin=PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .with_timestamp(timestamp=time_interval.timestamp)
            .with_end_time(end_time=time_interval.end_time)
            .build_all()
        )

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in inserted_events])

    async def test_update_core_event_with_none_should_update_correctly(
        self, user_with_events_with_end_time: tuple[Sequence[Event], MemberUser]
    ):
        """
        Updating existing value to null should update the field to null. We had issue with exclude_none where updates
        where fields were null were ignored
        """
        expected_events, user = user_with_events_with_end_time
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        assert all((e.end_time is not None for e in expected_events))

        update_input = [
            UpdatableEventInputBuilder()
            .with_type_id(type_id=e.type_id())
            .with_id(id=e.id)
            .with_timestamp(timestamp=e.timestamp)
            .with_end_time(end_time=None)
            .build()
            for e in expected_events
        ]
        request_input = UpdateEventAPIRequestInput(documents=update_input)

        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        updated_events = payload.documents
        assert len(updated_events) == len(expected_events)
        for e in updated_events:
            assert e.end_time is None

    @pytest.fixture
    async def user_with_groups(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[list[EventGroup], MemberUser]:
        user: MemberUser = await user_factory()
        event_groups = EventGroupBuilder().with_owner_id(owner_id=user.user_uuid).build_n()

        inserted_groups = await event_repo.insert(events=event_groups, force_strong_consistency=True)

        yield inserted_groups, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in inserted_groups])

    async def test_update_groups_endpoint_passes(
        self,
        user_with_groups: tuple[list[EventGroup], MemberUser],
        event_repo: EventRepository,
    ):

        # Arrange
        existing_groups, user = user_with_groups
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        update_groups_input = [UpdateEventGroupInputBuilder().with_id(e.id).build() for e in existing_groups]

        request_input = UpdateEventAPIRequestInput(documents=update_groups_input)

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        updated_groups = payload.documents

        assert len(updated_groups) == len(update_groups_input)

        for updated_group, expected_group in zip(
            sorted(updated_groups, key=lambda e: e.id), sorted(update_groups_input, key=lambda e: e.id)
        ):
            assert updated_group.id == expected_group.id
            assert updated_group.type == expected_group.type
            assert updated_group.name == expected_group.name
            assert updated_group.timestamp == expected_group.timestamp
            assert updated_group.end_time == expected_group.end_time
            assert updated_group.duration == (
                (updated_group.end_time - updated_group.timestamp).total_seconds() if updated_group.end_time else 0
            )

        # Teardown
        await event_repo.delete_by_id([(e.id, TypeResolver.get_event(e.type)) for e in updated_groups])
