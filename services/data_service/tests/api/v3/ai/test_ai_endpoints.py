import logging
from typing import Awaitable, Callable
from uuid import UUID
from zoneinfo import ZoneInfo

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.member_user.member_user import Member<PERSON>ser
from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings, MemberUserSettingsGeneral
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.payload.exercise_payload_builders import (
    CardioPayloadBuilder,
)
from services.base.tests.domain.builders.template.payload.nutrition_payload_builders import FoodPayloadBuilder
from services.base.tests.domain.builders.template.payload.symptom_payload_builder import SymptomPayloadBuilder
from services.data_service.api.urls import AIEndpointUrls
from services.data_service.api.v3.models.request.ai.suggest_event_request_input import SuggestEventRequestInput
from services.data_service.application.use_cases.events.models.content.insert_content_inputs import InsertVideoInput
from services.data_service.application.use_cases.events.models.exercise.insert_exercise_inputs import (
    InsertExerciseInput,
)
from services.data_service.application.use_cases.events.models.insert_event_input import InsertEventInput
from services.data_service.application.use_cases.events.models.insert_symptom_input import InsertSymptomInput
from services.data_service.application.use_cases.events.models.nutrition.insert_nutrition_inputs import InsertFoodInput
from services.data_service.tests.api.common_rpc_calls import _call_post_endpoint
from services.data_service.type_resolver import TypeResolver


class UserWithTemplates:
    def __init__(self, user: MemberUser, timezone: ZoneInfo, templates: list[EventTemplate]):
        self.user = user
        self.user_timezone = timezone
        self.templates = templates
        self._template_map = {t.id: t for t in templates}

    def get_user_template(self, template_id: UUID) -> EventTemplate | None:
        return self._template_map.get(template_id)


class TestAIEndpoints:
    @pytest.fixture
    async def user_with_templates(
        self,
        user_factory: Callable[[], Awaitable[MemberUser]],
        template_repo: TemplateRepository,
        member_user_settings_repo: MemberUserSettingsRepository,
    ):
        user = await user_factory()
        user_timezone = ZoneInfo("America/New_York")
        # Save the user timezone
        await member_user_settings_repo.insert_or_update(
            settings=MemberUserSettings(
                user_uuid=user.user_uuid,
                general=MemberUserSettingsGeneral(timezone=user_timezone),
            )
        )

        # Specific templates to match against
        curry_template_name = "yellow thai curry"
        yellow_thai_curry_template = (
            EventTemplateBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_name(curry_template_name)
            .with_document_name(curry_template_name)
            .with_tags(["curry"])
            .with_document(document=FoodPayloadBuilder().with_name(curry_template_name).with_tags(["curry"]).build())
            .build()
        )
        run_template_name = "run"
        running_template = (
            EventTemplateBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_name(run_template_name)
            .with_document_name(run_template_name)
            .with_tags(["run"])
            .with_document(document=CardioPayloadBuilder().with_name(run_template_name).with_tags(["run"]).build())
            .build()
        )
        symptom_template_name = "headache"
        symptom_template = (
            EventTemplateBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_name(symptom_template_name)
            .with_document_name(symptom_template_name)
            .with_tags(["headache"])
            .with_document(
                document=SymptomPayloadBuilder().with_name(symptom_template_name).with_tags(["headache"]).build()
            )
            .build()
        )
        user_with_templates = UserWithTemplates(
            user=user,
            timezone=user_timezone,
            templates=[
                yellow_thai_curry_template,
                running_template,
                symptom_template,
            ],
        )
        other_templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n(n=100)
        all_templates = [yellow_thai_curry_template, running_template, symptom_template]
        all_templates.extend(other_templates)
        await template_repo.insert(
            templates=all_templates,
            force_strong_consistency=True,
        )
        yield user_with_templates
        await template_repo.delete_by_id(ids=[yellow_thai_curry_template.id] + [t.id for t in other_templates])

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "query",
        [
            "I had yellow thai curry for lunch yesterday",
            "I went for a run",
            "I had severe headache today",
        ],
    )
    @pytest.mark.skip("Honza broke this")
    async def test_suggest_event_endpoint_uses_templates(self, user_with_templates: UserWithTemplates, query: str):
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_with_templates.user.user_uuid)}"}

        request_builder = SuggestEventRequestInput(query=query)

        # Act
        response = await _call_post_endpoint(
            request_url=AIEndpointUrls.SUGGEST_EVENT,
            json=request_builder.model_dump(by_alias=True),
            headers=headers,
            retry=False,
        )
        assert response.status_code == status.HTTP_200_OK, response.content
        body = response.json()
        assert body[EventFields.TYPE]

        event_type = TypeResolver.get_insert_event_input(body[EventFields.TYPE])
        event = event_type(**body)
        assert event.template_id

        user_template = user_with_templates.get_user_template(event.template_id)
        assert user_template
        assert str(user_template.document_type) == str(event.type)

        assert event.template_id == user_template.id
        assert user_template.document.tags[0] in event.tags

    @pytest.mark.integration
    async def test_suggest_event_does_not_use_templates_if_no_templates_are_provided(
        self, user_with_templates: UserWithTemplates
    ):
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_with_templates.user.user_uuid)}"}

        request_builder = SuggestEventRequestInput(query="i had a big mac curry for lunch yesterday")

        # Act
        response = await _call_post_endpoint(
            request_url=AIEndpointUrls.SUGGEST_EVENT,
            json=request_builder.model_dump(by_alias=True),
            headers=headers,
            retry=False,
        )

        assert response.status_code == status.HTTP_200_OK, response.content
        body = response.json()
        assert body[EventFields.TYPE]

        event_type = TypeResolver.get_insert_event_input(body[EventFields.TYPE])
        event = event_type(**body)
        logging.info(f"event: {event.model_dump(by_alias=True)}")
        assert not event.template_id

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "query, expected_event_type",
        [
            ("i had yellow thai curry for lunch yesterday", InsertFoodInput),
            ("i worked out", InsertExerciseInput),
            ("i watched a movie on netflix", InsertVideoInput),
            ("i have a headache", InsertSymptomInput),
        ],
    )
    async def test_suggest_event_endpoint_picks_correct_event_type(
        self,
        user_with_templates: UserWithTemplates,
        query: str,
        expected_event_type: type[InsertEventInput],
    ):
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_with_templates.user.user_uuid)}"}

        request_builder = SuggestEventRequestInput(query=query)

        # Act
        response = await _call_post_endpoint(
            request_url=AIEndpointUrls.SUGGEST_EVENT,
            json=request_builder.model_dump(by_alias=True),
            headers=headers,
            retry=False,
        )
        assert response.status_code == status.HTTP_200_OK, response.content
        body = response.json()
        assert body[EventFields.TYPE]

        event_type = TypeResolver.get_insert_event_input(body[EventFields.TYPE])
        assert event_type == expected_event_type

    @pytest.mark.integration
    async def test_suggest_event_endpoint_uses_users_timezone_and_end_time(
        self,
        user_with_templates: UserWithTemplates,
    ):
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user_with_templates.user.user_uuid)}"}

        request_builder = SuggestEventRequestInput(query="play football at 10am for 1 hour")

        # Act
        response = await _call_post_endpoint(
            request_url=AIEndpointUrls.SUGGEST_EVENT,
            json=request_builder.model_dump(by_alias=True),
            headers=headers,
            retry=False,
        )
        assert response.status_code == status.HTTP_200_OK, response.content
        body = response.json()
        assert body[EventFields.TYPE]

        event_type = TypeResolver.get_insert_event_input(body[EventFields.TYPE])
        event = event_type(**body)
        assert event.timestamp
        assert event.end_time
        assert event.timestamp.utcoffset() == user_with_templates.user_timezone.utcoffset(event.timestamp)
        assert event.end_time.utcoffset() == user_with_templates.user_timezone.utcoffset(event.end_time)
