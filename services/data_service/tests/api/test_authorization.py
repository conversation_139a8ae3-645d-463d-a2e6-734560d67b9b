from starlette import status

from services.data_service.api.activity_endpoints import activity_router
from services.data_service.api.data_summary_endpoints import summary_router
from services.data_service.api.delete_endpoints import delete_router
from services.data_service.api.diary_endpoints import diary_router
from services.data_service.api.environment_v2_endpoints import environment_router
from services.data_service.api.fetch_endpoints import fetch_router
from services.data_service.api.location_endpoints import location_router
from services.data_service.api.measure_endpoints import measure_router
from services.data_service.api.temp_plan_endpoints import temp_plan_router
from services.data_service.api.update_endpoints import update_router
from services.data_service.api.v1.data_crud_endpoints import data_crud_router
from services.data_service.api.v1.extensions.extension_directory_endpoints import extension_directory_router
from services.data_service.api.v1.extensions.extension_providers_endpoints import extension_providers_router
from services.data_service.api.v1.extensions.extension_results_endpoints import extension_router
from services.data_service.api.v3.aggregation_endpoints import aggregation_router
from services.data_service.api.v3.assets_endpoints import assets_router
from services.data_service.api.v3.document_endpoints import document_router
from services.data_service.api.v3.event_endpoints import event_router
from services.data_service.api.v3.plan_endpoints import plan_router
from services.data_service.api.v3.template_endpoints import template_router
from services.data_service.api.v3.use_case_endpoints import use_case_router
from services.data_service.tests.api.conftest import get_path_operation


def test_data_endpoints_unauthorized(test_client):
    for route in (
        *activity_router.routes,
        *aggregation_router.routes,
        *assets_router.routes,
        *data_crud_router.routes,
        *delete_router.routes,
        *diary_router.routes,
        *document_router.routes,
        *fetch_router.routes,
        *environment_router.routes,
        *event_router.routes,
        *extension_directory_router.routes,
        *extension_providers_router.routes,
        *extension_router.routes,
        *location_router.routes,
        *measure_router.routes,
        *plan_router.routes,
        *summary_router.routes,
        *temp_plan_router.routes,
        *template_router.routes,
        *update_router.routes,
        *use_case_router.routes,
    ):
        for method in route.methods:
            response = get_path_operation(method=method, test_client=test_client)(route.path)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
