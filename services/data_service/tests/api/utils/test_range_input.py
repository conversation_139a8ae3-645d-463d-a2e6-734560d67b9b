import datetime
from typing import Dict

import pytest
from fastapi.exceptions import RequestValidationError

from services.base.application.database.models.filter_types import (
    CreatedAtRangeFilter,
    RangeFilter,
    TimestampRangeFilter,
    UpdatedAtRangeFilter,
)
from services.data_service.api.utils.get_range_input import get_range_input


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        (
            {
                "range_field": "timestamp",
                "gte": "2021-03-19T08:54:03.586159Z",
                "lte": "2024-04-17T17:29:52.471000Z",
            },
            (
                TimestampRangeFilter(
                    name="timestamp",
                    gte=datetime.datetime(2021, 3, 19, 8, 54, 3, 586159, tzinfo=datetime.timezone.utc),
                    lte=datetime.datetime(2024, 4, 17, 17, 29, 52, 471000, tzinfo=datetime.timezone.utc),
                )
            ),
        ),
        (
            {
                "range_field": "system_properties.created_at",
                "gte": "2021-03-19T08:54:03.586159Z",
                "lte": "2024-04-17T17:29:52.471000Z",
            },
            (
                CreatedAtRangeFilter(
                    name="system_properties.created_at",
                    gte=datetime.datetime(2021, 3, 19, 8, 54, 3, 586159, tzinfo=datetime.timezone.utc),
                    lte=datetime.datetime(2024, 4, 17, 17, 29, 52, 471000, tzinfo=datetime.timezone.utc),
                )
            ),
        ),
        (
            {
                "range_field": "system_properties.updated_at",
                "gte": "2021-03-19T08:54:03.586159Z",
                "lte": "2024-04-17T17:29:52.471000Z",
            },
            (
                UpdatedAtRangeFilter(
                    name="system_properties.updated_at",
                    gte=datetime.datetime(2021, 3, 19, 8, 54, 3, 586159, tzinfo=datetime.timezone.utc),
                    lte=datetime.datetime(2024, 4, 17, 17, 29, 52, 471000, tzinfo=datetime.timezone.utc),
                )
            ),
        ),
        (
            {
                "range_field": "metadata.data_integrity",
                "gte": "2",
                "lte": "4",
            },
            (RangeFilter(name="metadata.data_integrity", gte="2", lte="4")),
        ),
        (
            {
                "range_field": "data.custom.field",
                "gte": "1",
                "lte": "2",
            },
            (RangeFilter(name="data.custom.field", gte="1", lte="2")),
        ),
        (
            {"range_field": "data.custom.field", "gte": "1", "lte": None},
            (RangeFilter(name="data.custom.field", gte="1")),
        ),
        (
            {"range_field": "system_properties.updated_at", "gte": "2021-03-19T08:54:03.586159Z", "lte": None},
            (
                UpdatedAtRangeFilter(
                    name="system_properties.updated_at",
                    gte=datetime.datetime(2021, 3, 19, 8, 54, 3, 586159, tzinfo=datetime.timezone.utc),
                )
            ),
        ),
    ],
)
def test_get_range_input_should_pass(test_input: Dict, expected_result: RangeFilter):
    range_input = get_range_input(**test_input)
    assert range_input.name == expected_result.name
    assert range_input.gte == expected_result.gte
    assert range_input.lte == expected_result.lte


@pytest.mark.parametrize(
    "test_input",
    [
        {
            "range_field": "data.custom.field",
            "gte": "",
            "lte": "1",
        },
        {
            "range_field": "timestamp",
            "gte": "2021-03-19T08:54:03.586159",
            "lte": "2020-04-17T17:29:52.471000",
        },
        {
            "range_field": "timestamp",
            "gte": "",
            "lte": "",
        },
        {
            "range_field": "timestamp",
        },
        {
            "range_field": None,
            "lte": "2020-04-17T17:29:52.471000",
        },
        {
            "range_field": None,
            "gte": "2021-03-19T08:54:03.586159",
        },
    ],
)
def test_get_range_input_raise_value_error(test_input: Dict):
    with pytest.raises((ValueError, RequestValidationError)):
        get_range_input(**test_input)
