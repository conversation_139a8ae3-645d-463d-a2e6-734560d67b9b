import json
from datetime import datetime, timedelta, timezone

from httpx import Response
from pydantic import AwareDatetime

from services.base.application.utils.urls import join_as_url
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.metadata import DeprIdentifiableMetadataModel
from services.data_service.api.urls import UpdateEndpointUrls
from services.data_service.tests.api.common_rpc_calls import _call_patch_endpoint


class TestUtils:
    @staticmethod
    async def call_update(data_type: DataType, updated_doc: DeprIdentifiableMetadataModel, headers) -> Response:
        request_url = join_as_url(
            base_url=UpdateEndpointUrls.BY_ID,
            query_params={"data_type": data_type.value},
        )

        return await _call_patch_endpoint(
            request_url=request_url,
            json=json.loads(updated_doc.model_dump_json(by_alias=True)),
            headers=headers,
        )

    @staticmethod
    def _sanitize_date(date: AwareDatetime | str) -> AwareDatetime:
        if isinstance(date, str):
            return datetime.fromisoformat(date)
        return date

    @staticmethod
    def is_date_within_one_minute(date: AwareDatetime | str) -> bool:
        sanitized_date = TestUtils._sanitize_date(date=date)
        return (datetime.now(timezone.utc) - sanitized_date) <= timedelta(seconds=60)

    @staticmethod
    def to_assertable_dict(model: DeprIdentifiableMetadataModel):
        """
        Converts model to dict for assertion purposes, therefore removing user_uuid and updated_at from metadata, since
        those are not expected to be in the response
        """
        return json.loads(
            model.model_dump_json(
                exclude={
                    DocumentLabels.METADATA: {DocumentLabels.USER_UUID},
                    DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.UPDATED_AT},
                },
                by_alias=True,
            )
        )
