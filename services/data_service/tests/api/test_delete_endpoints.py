from typing import Callable, <PERSON><PERSON>

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.diary_event_builder import DiaryEventBuilder
from services.data_service.api.response_models.delete_document_by_id_api_response import DeleteDocumentByIdAPIResponse
from services.data_service.api.urls import DeleteEndpointUrls
from services.data_service.tests.api.common_rpc_calls import _call_delete_endpoint


class TestDeleteEndpoint:
    @pytest.fixture
    async def diary_event_and_user(
        self, depr_event_repository: DeprEventRepository, user_factory: Callable[[], MemberUser]
    ) -> Tuple[DiaryEvents, MemberUser]:
        user: MemberUser = await user_factory()
        diary_event = DiaryEventBuilder().with_user_uuid(user.user_uuid).build()
        inserted_diary_event = (await depr_event_repository.insert(models=[diary_event]))[0]
        return inserted_diary_event, user

    async def test_delete_diary_event_by_id_should_pass(self, diary_event_and_user: Tuple[DiaryEvents, MemberUser]):
        # Arrange
        diary_event, user = diary_event_and_user
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        query_params = {"doc_id": diary_event.doc_id, "data_type": DataType.DiaryEvents.value}
        request_url = DeleteEndpointUrls.BY_ID

        response = await _call_delete_endpoint(request_url=request_url, query_params=query_params, headers=headers)

        assert response.status_code == status.HTTP_200_OK
        response_model = DeleteDocumentByIdAPIResponse(**response.json())
        assert response_model.doc_id == diary_event.doc_id
        assert response_model.deletion_succeeded
