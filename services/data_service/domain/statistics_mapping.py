from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.heart_rate import HeartRateFields
from services.base.domain.schemas.resting_heart_rate import RestingHeartRateFields
from services.base.domain.schemas.sleep import SleepFields, SleepSummaryFields
from services.base.domain.schemas.steps import StepsFields

statistics_aggregation_fields_per_data_type = {
    EventType.HeartRate: [
        HeartRateFields.BPM_MAX,
        HeartRateFields.BPM_MIN,
        HeartRateFields.BPM_AVG,
    ],
    EventType.RestingHeartRate: [
        RestingHeartRateFields.BPM_MIN,
        RestingHeartRateFields.BPM_MAX,
        RestingHeartRateFields.BPM_AVG,
    ],
    EventType.Sleep: [
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.EFFICIENCY}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.AWAKE_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.EVENTS_COUNT}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.FALL_ASLEEP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.AFTER_WAKEUP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.ASLEEP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.IN_BED_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.DEEP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.LIGHT_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.REM_SECONDS}",
    ],
    EventType.Steps: [StepsFields.STEPS],
}
