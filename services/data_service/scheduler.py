from datetime import time

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from services.data_service.application.workflows.forecast_summary_workflow.forecast_summary_workflow import (
    ForecastSummaryWorkflow,
)
from services.data_service.dependency_bootstrapper import DependencyBootstrapper


class Scheduler:
    def __init__(self, bootstrapper: DependencyBootstrapper):
        self.bootstrapper = bootstrapper
        self.scheduler = AsyncIOScheduler()
        self.scheduler.add_job(
            func=self._process_forecast_summary_workflow,
            trigger="cron",
            hour="*",
            max_instances=1,
            kwargs={"workflow": self.bootstrapper.get(interface=ForecastSummaryWorkflow)},
        )

    async def _process_forecast_summary_workflow(self, workflow: ForecastSummaryWorkflow):
        await workflow.run(local_time_of_triggering=time(hour=3))

    def start(self):
        self.scheduler.start()

    def shutdown(self):
        self.scheduler.shutdown()
