from typing import Optional

from pydantic import Field

from services.base.application.boundaries.output_models import IdentifiableOutputModelV2
from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import SystemPropertiesDocument
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.temp_plan import TempPlanDiaryEvent, TempPlanFields, TempPlanStreak


class TempPlanMetadataAPIOutput(BaseDataModel):
    archived_at: Optional[SerializableAwareDatetime] = Field(default=None, alias=DocumentLabels.ARCHIVED_AT)


class TempPlanAPIOutput(IdentifiableOutputModelV2, SystemPropertiesDocument):
    name_of_plan: str = Field(alias=TempPlanFields.NAME_OF_PLAN, min_length=1)
    start_time: SerializableAwareDatetime = Field(alias=TempPlanFields.START_TIME)
    last_modified_at: SerializableAwareDatetime = Field(alias=TempPlanFields.LAST_MODIFIED_AT)
    events: list[TempPlanDiaryEvent] = Field(alias=TempPlanFields.EVENTS, min_length=1)
    message: str = Field(alias=TempPlanFields.MESSAGE, min_length=1)
    is_urgent: bool = Field(alias=TempPlanFields.IS_URGENT)
    streak: TempPlanStreak = Field(alias=TempPlanFields.STREAK)
    absolute_schedule: bool = Field(alias=TempPlanFields.ABSOLUTE_SCHEDULE, default=False)
    next_scheduled_at: SerializableAwareDatetime = Field(alias=TempPlanFields.NEXT_SCHEDULED_AT)
    is_confirmation_required: bool = Field(alias=TempPlanFields.IS_CONFIRMATION_REQUIRED)
    interval: int = Field(alias=TempPlanFields.INTERVAL, ge=0)
    metadata: TempPlanMetadataAPIOutput
