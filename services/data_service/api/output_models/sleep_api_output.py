from typing import Literal

from pydantic import Field

from services.base.application.boundaries.output_models import EventOutputModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.sleep import SleepIdentifier, SleepSchema


class SleepAPIOutput(EventOutputModel, SleepSchema, SleepIdentifier):
    type: Literal[DataType.Sleep] = Field(alias=DocumentLabels.TYPE)
