from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import (
    RoundedFloat,
)
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.body_metric.blood_glucose import (
    BloodGlucoseCategory,
    BloodGlucoseFields,
    BloodGlucoseIdentifier,
    BloodGlucoseSpecimenSource,
    BloodGlucoseValueLimits,
)
from services.base.domain.schemas.events.body_metric.blood_pressure import (
    BloodPressureCategory,
    BloodPressureFields,
    BloodPressureIdentifier,
    BloodPressureValueLimits,
)
from services.base.domain.schemas.events.body_metric.body_metric import (
    BodyMetricCategory,
    BodyMetricFields,
    BodyMetricIdentifier,
)
from services.data_service.api.output_models.events.event_api_output_base import EventAPIOutputBase


class BodyMetricAPIOutput(EventAPIOutputBase, BodyMetricIdentifier):
    type: Literal[DataType.BodyMetric] = Field(alias=BodyMetricFields.TYPE)
    category: BodyMetricCategory = Field(alias=BodyMetricFields.CATEGORY)
    value: float = Field(alias=BodyMetricFields.VALUE, ge=-(10**9), le=10**9)


class BloodPressureAPIOutput(EventAPIOutputBase, BloodPressureIdentifier):
    type: Literal[DataType.BloodPressure] = Field(alias=BloodPressureFields.TYPE)
    category: BloodPressureCategory = Field(alias=BloodPressureFields.CATEGORY)
    diastolic: RoundedFloat = Field(
        alias=BloodPressureFields.DIASTOLIC,
        ge=BloodPressureValueLimits.MINIMUM_DIASTOLIC,
        le=BloodPressureValueLimits.MAXIMUM_DIASTOLIC,
    )
    systolic: RoundedFloat = Field(
        alias=BloodPressureFields.SYSTOLIC,
        ge=BloodPressureValueLimits.MINIMUM_SYSTOLIC,
        le=BloodPressureValueLimits.MAXIMUM_SYSTOLIC,
    )


class BloodGlucoseAPIOutput(EventAPIOutputBase, BloodGlucoseIdentifier):
    type: Literal[DataType.BloodGlucose] = Field(alias=BloodGlucoseFields.TYPE)
    category: BloodGlucoseCategory = Field(
        alias=BloodGlucoseFields.CATEGORY, default=BloodGlucoseCategory.BLOOD_GLUCOSE
    )
    value: RoundedFloat = Field(
        alias=BloodGlucoseFields.VALUE,
        ge=BloodGlucoseValueLimits.MINIMUM_VALUE,
        le=BloodGlucoseValueLimits.MAXIMUM_VALUE,
    )
    specimen_source: BloodGlucoseSpecimenSource = Field(
        alias=BloodGlucoseFields.SPECIMEN_SOURCE, default=BloodGlucoseSpecimenSource.UNKNOWN
    )
