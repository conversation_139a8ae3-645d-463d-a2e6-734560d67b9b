from pydantic import Field

from services.base.domain.enums.metadata_v3 import Origin, SourceService
from services.base.domain.schemas.events.document_base import EventMetadataFields
from services.base.domain.schemas.shared import BaseDataModel


class EventMetadataAPIOutput(BaseDataModel):
    origin: Origin = Field(alias=EventMetadataFields.ORIGIN)
    source_service: SourceService = Field(alias=EventMetadataFields.SOURCE_SERVICE)
