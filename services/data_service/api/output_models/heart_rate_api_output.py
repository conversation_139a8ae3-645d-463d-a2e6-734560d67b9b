from typing import Literal

from pydantic import Field

from services.base.application.boundaries.output_models import EventOutputModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.heart_rate import HeartRateIdentifier, HeartRateSchema


class HeartRateAPIOutput(EventOutputModel, HeartRateSchema, HeartRateIdentifier):
    type: Literal[DataType.HeartRate] = Field(alias=DocumentLabels.TYPE)
