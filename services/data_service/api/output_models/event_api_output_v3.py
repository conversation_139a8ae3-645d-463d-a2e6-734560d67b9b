from typing import Annotated

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.data_service.api.v3.models.response.event.event_v3_api_output import EventGroupAPIOutput
from services.data_service.type_resolver import TypeResolver

GroupV3APIOutput = EventGroupAPIOutput  # TODO Remove when we have base class (used only in tests)
EventV3APIOutput = Annotated[TypeResolver.EVENT_API_OUTPUTS_V3_UNION, Field(discriminator=DocumentLabels.TYPE)]
