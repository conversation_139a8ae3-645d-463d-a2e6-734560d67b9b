from typing import Literal

from pydantic import Field

from services.base.application.boundaries.output_models import EventOutputModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.location import LocationIdentifier, LocationSchema


class LocationAPIOutput(EventOutputModel, LocationSchema, LocationIdentifier):
    type: Literal[DataType.Location] = Field(alias=DocumentLabels.TYPE)
