import logging
from typing import Dict, List
from uuid import UUID

from fastapi import Body, Depends
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import BadRequestException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.data_service.application.enums.updatable_data_type import UpdatableDataType


def get_data_type_schema_model(
    data_type: UpdatableDataType,
    entry: Dict = Body(...),
    current_uuid: UUID = Depends(get_current_uuid),
) -> List[DeprEventModel]:
    schema_type = data_type.to_domain_model()
    try:
        entry[DocumentLabels.METADATA][DocumentLabels.USER_UUID] = current_uuid
        return [schema_type(**entry)]
    except KeyError as err:
        logging.info(f"Incorrect entry to update: {entry}, err: {err}")
        raise BadRequestException("Incorrect entry to update")
    except ValidationError as error:
        logging.info(f"Failed to deserialize entry: {entry}")
        raise RequestValidationError(error.errors())
