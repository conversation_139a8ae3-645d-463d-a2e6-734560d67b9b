from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID

from fastapi import Depends
from fastapi.exceptions import RequestValidationError
from pydantic import Field, ValidationError

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.time_input import TimeInput, TimeRangeInput
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization, Service
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.shared import BaseDataModel


def get_metadata_fields(
    organization: Optional[Organization] = None,
    service: Optional[Union[Service, str]] = None,
    important: Optional[bool] = None,
    urgent: Optional[bool] = None,
    tags: Optional[List[str]] = None,
    # compatibility param, remove afterwards
    provider: Optional[Provider] = None,
) -> MetadataParametersInputBoundary:
    return MetadataParametersInputBoundary(
        organization=organization if organization else Organization(provider.value) if provider else None,
        service=service,
        important=important,
        urgent=urgent,
        tags=tags,
    )


async def get_time_input(
    time_gte: datetime, time_lte: datetime, interval: Optional[str] = None
) -> TimeInput | TimeRangeInput:
    try:
        if not interval:
            return TimeRangeInput(time_gte=time_gte, time_lte=time_lte)
        return TimeInput(time_gte=time_gte, time_lte=time_lte, interval=interval)
    except ValidationError as error:
        raise RequestValidationError(error.errors())


class CommonFetchInputModel(BaseDataModel):
    user_uuid: UUID = Field(alias=DocumentLabels.USER_UUID)
    time_input: TimeInput = Field()
    re_fetch_most_recent: bool = Field(default=False)
    organization: Optional[Organization] = Field(alias=DocumentLabels.ORGANIZATION, default=None)
    metadata: MetadataParametersInputBoundary = Field(alias=DocumentLabels.METADATA)


class CommonFetchInputModelOptionalInterval(BaseDataModel):
    user_uuid: UUID = Field(alias=DocumentLabels.USER_UUID)
    time_input: TimeInput | TimeRangeInput
    re_fetch_most_recent: bool = Field(default=False)
    organization: Optional[Organization] = Field(alias=DocumentLabels.ORGANIZATION, default=None)
    metadata: MetadataParametersInputBoundary = Field(alias=DocumentLabels.METADATA)


async def get_common_fetch_input(
    time_input: TimeInput = Depends(get_time_input),
    re_fetch_most_recent: bool = False,
    user_uuid: UUID = Depends(get_current_uuid),
    metadata: MetadataParametersInputBoundary = Depends(get_metadata_fields),
):
    return CommonFetchInputModel(
        re_fetch_most_recent=re_fetch_most_recent,
        time_input=time_input,
        user_uuid=user_uuid,
        metadata=metadata,
    )


async def get_common_fetch_input_optional_interval(
    time_input: Union[TimeInput, TimeRangeInput] = Depends(get_time_input),
    re_fetch_most_recent: bool = False,
    user_uuid: UUID = Depends(get_current_uuid),
    metadata: MetadataParametersInputBoundary = Depends(get_metadata_fields),
):
    return CommonFetchInputModelOptionalInterval(
        re_fetch_most_recent=re_fetch_most_recent,
        time_input=time_input,
        user_uuid=user_uuid,
        metadata=metadata,
    )
