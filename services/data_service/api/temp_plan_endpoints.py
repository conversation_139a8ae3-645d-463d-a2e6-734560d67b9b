from uuid import UUID

from fastapi import APIRouter, Body, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, PlanEndpointRoutes
from services.data_service.api.output_models.plans.temp_plan_api_output import TempPlanAPIOutput
from services.data_service.api.request_models.plans.load_temp_plan_api_request_input import LoadTempPlanAPIRequestInput
from services.data_service.api.request_models.plans.update_temp_plan_api_request_input import (
    UpdateTempPlanAPIRequestInput,
)
from services.data_service.application.use_cases.plans.archive_temp_plan_use_case import ArchiveTempPlanUseCase
from services.data_service.application.use_cases.plans.list_temp_plans_use_case import ListTempPlansUseCase
from services.data_service.application.use_cases.plans.load_temp_plan_use_case import LoadTempPlanUseCase
from services.data_service.application.use_cases.plans.models.load_temp_plan_input_boundary import (
    LoadTempPlanInputBoundary,
)
from services.data_service.application.use_cases.plans.models.update_temp_plan_input_boundary import (
    UpdateTempPlanInputBoundary,
)
from services.data_service.application.use_cases.plans.update_temp_plan_use_case import UpdateTempPlanUseCase

temp_plan_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION1_PREFIX}{DataServicePrefixes.PLAN_PREFIX}",
    tags=["plan"],
    responses={404: {"description": "Not found"}},
)


@temp_plan_router.get(PlanEndpointRoutes.TEMP)
async def list_temp_plans_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    target_use_case: ListTempPlansUseCase = Injected(ListTempPlansUseCase),
) -> CommonDocumentsResponse[TempPlanAPIOutput]:
    plans = await target_use_case.execute_async(user_uuid=user_uuid)
    if not plans:
        raise NoContentException(message="no matching plans found")

    output = [TempPlanAPIOutput.map(model=plan) for plan in plans]

    return CommonDocumentsResponse[TempPlanAPIOutput](documents=output)


@temp_plan_router.patch(PlanEndpointRoutes.TEMP_ARCHIVE)
async def archive_temp_plans_endpoint(
    plan_ids: list[UUID] = Query(...),
    user_uuid: UUID = Depends(get_current_uuid),
    target_use_case: ArchiveTempPlanUseCase = Injected(ArchiveTempPlanUseCase),
) -> CommonDocumentsResponse[TempPlanAPIOutput]:
    plans = await target_use_case.execute_async(user_uuid=user_uuid, plan_ids=plan_ids)
    if not plans:
        raise NoContentException(message="no matching plans found to archive")

    return CommonDocumentsResponse[TempPlanAPIOutput](
        documents=[TempPlanAPIOutput(**p.model_dump(by_alias=True)) for p in plans]
    )


@temp_plan_router.patch(PlanEndpointRoutes.TEMP)
async def update_temp_plans_endpoint(
    input_data: UpdateTempPlanAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    target_use_case: UpdateTempPlanUseCase = Injected(UpdateTempPlanUseCase),
) -> CommonDocumentsResponse[TempPlanAPIOutput]:
    try:
        plans = await target_use_case.execute_async(
            user_uuid=user_uuid, input_boundary=UpdateTempPlanInputBoundary.map(model=input_data)
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="plan duplicates found in the update payload") from err
    if not plans:
        raise NoContentException(message="no matching plans found to update")

    return CommonDocumentsResponse[TempPlanAPIOutput](
        documents=[TempPlanAPIOutput(**p.model_dump(by_alias=True)) for p in plans]
    )


@temp_plan_router.post(PlanEndpointRoutes.TEMP)
async def load_temp_plans_endpoint(
    input_data: LoadTempPlanAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: LoadTempPlanUseCase = Injected(LoadTempPlanUseCase),
) -> CommonDocumentsResponse[TempPlanAPIOutput]:
    try:
        plans = await use_case.execute_async(
            input_data=LoadTempPlanInputBoundary.map(model=input_data), user_uuid=user_uuid
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate plan detected. A plan with the same details already exists in the system."
        ) from err
    return CommonDocumentsResponse[TempPlanAPIOutput](
        documents=[TempPlanAPIOutput(**p.model_dump(by_alias=True)) for p in plans]
    )
