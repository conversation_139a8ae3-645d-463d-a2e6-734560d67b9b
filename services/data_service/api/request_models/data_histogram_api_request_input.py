from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.schemas.query.aggregations import DateHistogramAggregation
from services.data_service.api.queries.event_query_api import EventQueryAPI
from services.data_service.application.use_cases.date_histogram_use_case import DateHistogramUseCaseInputBoundary


class DataHistogramAPIRequestInput(EventQueryAPI):
    aggregation: DateHistogramAggregation

    @staticmethod
    def to_input_boundary(
        request_input: DataHistogramAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> DateHistogramUseCaseInputBoundary:
        return DateHistogramUseCaseInputBoundary(
            query=request_input.to_query(),
            aggregation=request_input.aggregation,
            owner_id=owner_id,
        )
