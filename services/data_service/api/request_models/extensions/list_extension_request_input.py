import json
import logging
from typing import List, Optional
from uuid import UUID

from fastapi import Depends, Query
from fastapi.exceptions import RequestValidationError
from pydantic import Field

from services.base.application.database.models.filter_types import RangeFilter
from services.base.application.database.models.sorts import Sort, SortOrder
from services.base.application.utils.encoders import decode_base_64
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.api.utils.get_range_input import get_range_input
from services.data_service.application.models.list_extension_runs_continuation_token import (
    ListExtensionRunsContinuationToken,
)


class ListExtensionRunRequestInput(BaseDataModel):
    limit: int = Field(default=100, gt=0, le=1000)
    continuation_token: Optional[ListExtensionRunsContinuationToken] = Field(default=None)
    range_filter: Optional[RangeFilter] = Field(default=None)
    sort: Sort = Field(...)
    extension_ids: Optional[List[UUID]] = Field(default=None)


class ListExtensionResultsRequestInput(BaseDataModel):
    run_id: UUID = Field()


def get_list_extension_runs_request_input(
    limit: int = Query(default=100, gt=0, le=1000),
    continuation_token: Optional[str] = Query(default=None),
    range_filter: Optional[RangeFilter] = Depends(get_range_input),
    sort_order: SortOrder = Query(default=SortOrder.DESCENDING),
    extension_ids: Optional[List[UUID]] = Query(default=None, min_length=1),
) -> ListExtensionRunRequestInput:
    token: Optional[ListExtensionRunsContinuationToken] = None
    if continuation_token:
        try:
            token = ListExtensionRunsContinuationToken(**json.loads(decode_base_64(encoded_token=continuation_token)))
        except Exception as error:
            logging.exception(error)
            raise RequestValidationError("Unable to decode continuation token.")
        if not extension_ids == token.extension_ids:
            raise RequestValidationError(
                "Extension ids differ from values that the continuation token was created with."
            )
    return ListExtensionRunRequestInput(
        limit=limit,
        range_filter=range_filter,
        continuation_token=token,
        extension_ids=extension_ids,
        sort=Sort(name=DocumentLabels.TIMESTAMP, order=sort_order),
    )


def get_list_extension_results_request_input(
    run_id: UUID = Query(description="Run id of the parent document"),
) -> ListExtensionResultsRequestInput:
    return ListExtensionResultsRequestInput(run_id=run_id)
