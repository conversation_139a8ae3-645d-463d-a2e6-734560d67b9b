from uuid import UUID

from fastapi import API<PERSON><PERSON>er, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadR<PERSON><PERSON><PERSON>xception,
    DuplicateDocumentsFound,
    Incorrect<PERSON>perationException,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, PlanEndpointRoutes
from services.data_service.api.v3.mapper.eventv3_api_output_mapper import EventV3APIOutputMapper
from services.data_service.api.v3.models.request.plan.complete_plans_api_request_input import (
    CompletePlansAPIRequestInput,
)
from services.data_service.api.v3.models.request.plan.insert_plans_api_request_input import InsertPlansAPIRequestInput
from services.data_service.api.v3.models.request.plan.search_plans_request_input import SearchPlansRequestInput
from services.data_service.api.v3.models.request.plan.update_plans_api_request_input import UpdatePlansAPIRequestInput
from services.data_service.api.v3.models.response.plan.complete_plan_api_output import CompletePlanAPIOutput
from services.data_service.api.v3.models.response.plan.plan_api_output import PlanAPIOutput
from services.data_service.application.use_cases.plans.archive_plans_use_case import ArchivePlansUseCase
from services.data_service.application.use_cases.plans.complete_plans_use_case import CompletePlansUseCase
from services.data_service.application.use_cases.plans.insert_plans_use_case import InsertPlansUseCase
from services.data_service.application.use_cases.plans.models.complete_plans_input_boundary import (
    CompletePlansInputBoundary,
)
from services.data_service.application.use_cases.plans.models.insert_plans_input_boundary import (
    InsertPlansInputBoundary,
)
from services.data_service.application.use_cases.plans.models.search_plans_input_boundary import (
    SearchPlansInputBoundary,
)
from services.data_service.application.use_cases.plans.models.update_plan_input_boundary import (
    UpdatePlansInputBoundary,
)
from services.data_service.application.use_cases.plans.search_plans_use_case import SearchPlansUseCase
from services.data_service.application.use_cases.plans.update_plans_use_case import UpdatePlansUseCase

plan_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.PLAN_PREFIX}",
    tags=["plan"],
    responses={404: {"description": "Not found"}},
)


@plan_router.post(PlanEndpointRoutes.SEARCH)
async def search_plans_endpoint(
    use_case: SearchPlansUseCase = Injected(SearchPlansUseCase),
    input_boundary: SearchPlansInputBoundary = Depends(SearchPlansRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[PlanAPIOutput]:
    plans = await use_case.execute_async(input_boundary=input_boundary)
    if not plans:
        raise NoContentException(message="no matching plans found")

    output = [PlanAPIOutput(**p.model_dump(by_alias=True)) for p in plans]
    return CommonDocumentsResponse[PlanAPIOutput](documents=output)


@plan_router.patch(PlanEndpointRoutes.ARCHIVE)
async def archive_plans_endpoint(
    plan_ids: list[UUID] = Query(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ArchivePlansUseCase = Injected(ArchivePlansUseCase),
) -> CommonDocumentsResponse[PlanAPIOutput]:
    try:
        plans = await use_case.execute_async(owner_id=owner_id, plan_ids=plan_ids)
        return CommonDocumentsResponse[PlanAPIOutput](
            documents=[PlanAPIOutput(**p.model_dump(by_alias=True)) for p in plans]
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@plan_router.patch(PlanEndpointRoutes.BASE)
async def update_plans_endpoint(
    input_boundary: UpdatePlansInputBoundary = Depends(UpdatePlansAPIRequestInput.to_input_boundary),
    use_case: UpdatePlansUseCase = Injected(UpdatePlansUseCase),
) -> CommonDocumentsResponse[PlanAPIOutput]:
    try:
        plans = await use_case.execute_async(input_boundary=input_boundary)
        return CommonDocumentsResponse[PlanAPIOutput](
            documents=[PlanAPIOutput(**p.model_dump(by_alias=True)) for p in plans]
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="plan duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@plan_router.post(PlanEndpointRoutes.BASE)
async def insert_plans_endpoint(
    input_boundary: InsertPlansInputBoundary = Depends(InsertPlansAPIRequestInput.to_input_boundary),
    use_case: InsertPlansUseCase = Injected(InsertPlansUseCase),
) -> CommonDocumentsResponse[PlanAPIOutput]:
    try:
        plans = await use_case.execute_async(input_boundary=input_boundary)
        return CommonDocumentsResponse[PlanAPIOutput](
            documents=[PlanAPIOutput(**p.model_dump(by_alias=True)) for p in plans]
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate plan detected. A plan with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@plan_router.post(PlanEndpointRoutes.COMPLETE)
async def complete_plans_endpoint(
    input_boundary: CompletePlansInputBoundary = Depends(CompletePlansAPIRequestInput.to_input_boundary),
    use_case: CompletePlansUseCase = Injected(CompletePlansUseCase),
) -> CommonDocumentsResponse[CompletePlanAPIOutput]:
    try:
        output_boundary = await use_case.execute_async(input_boundary=input_boundary)
        return CommonDocumentsResponse[CompletePlanAPIOutput](
            documents=[
                CompletePlanAPIOutput(
                    **d.plan.model_dump(by_alias=True), events=[EventV3APIOutputMapper.map(e) for e in d.events]
                )
                for d in output_boundary.documents
            ]
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
