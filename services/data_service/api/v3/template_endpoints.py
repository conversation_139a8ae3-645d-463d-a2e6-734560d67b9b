from uuid import UUID

from fastapi import APIRouter, Body, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadR<PERSON><PERSON>Exception,
    DuplicateDocumentsFound,
    IncorrectOperationException,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, TemplateRoutes
from services.data_service.api.v3.mapper.template_api_output_mapper import TemplateAPIOutputMapper
from services.data_service.api.v3.models.request.template.insert_template_request_input import (
    InsertTemplateAPIRequestInput,
)
from services.data_service.api.v3.models.request.template.search_templates_request_input import (
    SearchTemplatesRequestInput,
)
from services.data_service.api.v3.models.request.template.update_template_request_input import (
    UpdateTemplateAPIRequestInput,
)
from services.data_service.api.v3.models.response.template.template_api_output import (
    TemplateAPIOutput,
)
from services.data_service.application.use_cases.templates.archive_template_use_case import (
    ArchiveTemplateUseCase,
)
from services.data_service.application.use_cases.templates.insert_template_use_case import (
    InsertTemplateUseCase,
)
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.models.search_templates_input_boundary import (
    SearchTemplatesInputBoundary,
)
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.search_template_use_case import SearchTemplatesUseCase
from services.data_service.application.use_cases.templates.update_template_use_case import (
    UpdateTemplatesUseCase,
)

template_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.TEMPLATE_PREFIX}",
    tags=["template"],
    responses={404: {"description": "Not found"}},
)


@template_router.post(TemplateRoutes.SEARCH)
async def search_templates_endpoint(
    use_case: SearchTemplatesUseCase = Injected(SearchTemplatesUseCase),
    input_boundary: SearchTemplatesInputBoundary = Depends(SearchTemplatesRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    documents = await use_case.execute_async(input_boundary=input_boundary)
    if not documents:
        raise NoContentException("no templates found")

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.post(TemplateRoutes.BASE)
async def insert_template_endpoint(
    use_case: InsertTemplateUseCase = Injected(InsertTemplateUseCase),
    input_data: InsertTemplateAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(
            boundary=InsertTemplateInputBoundary.map(model=input_data),
            owner_id=owner_id,
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate templates detected. A template with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.patch(TemplateRoutes.BASE)
async def update_template_endpoint(
    use_case: UpdateTemplatesUseCase = Injected(UpdateTemplatesUseCase),
    input_data: UpdateTemplateAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(
            input_boundary=UpdateTemplateInputBoundary.map(model=input_data),
            owner_id=owner_id,
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="template duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.patch(TemplateRoutes.ARCHIVE)
async def archive_templates_endpoint(
    use_case: ArchiveTemplateUseCase = Injected(ArchiveTemplateUseCase),
    template_ids: list[UUID] = Query(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(owner_id=owner_id, template_ids=template_ids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])
