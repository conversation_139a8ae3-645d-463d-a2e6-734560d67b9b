from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template
from services.data_service.api.v3.models.response.template.template_api_output import (
    EventTemplateAPIOutput,
    GroupTemplateAPIOutput,
    TemplateAPIOutput,
)


class TemplateAPIOutputMapper:
    _mapping: dict[type, type] = {
        EventTemplate: EventTemplateAPIOutput,
        GroupTemplate: GroupTemplateAPIOutput,
    }

    @classmethod
    def map(cls, document: Template) -> TemplateAPIOutput:
        doc_type = type(document)
        output_schema = cls._mapping.get(doc_type)
        if not output_schema:
            raise ValueError(f"No mapping found for document type: {doc_type}")
        return output_schema(**document.model_dump(by_alias=True))
