from uuid import UUID

from fastapi import API<PERSON>outer, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
)
from services.data_service.api.constants import DataServicePrefixes, EventEndpointRoutes
from services.data_service.api.output_models.event_api_output_v3 import EventV3APIOutput
from services.data_service.api.request_models.modify_event_assets_api_request_input import (
    ModifyEventAssetsAPIRequestInput,
)
from services.data_service.api.v1.continuation_token_marshaller import ContinuationTokenMarshaller
from services.data_service.api.v1.event_api_output_mapper import EventAPIOutputMapper
from services.data_service.api.v3.mapper.eventv3_api_output_mapper import EventV3<PERSON>IOutputMapper
from services.data_service.api.v3.models.request.event.delete_event_api_request_input import DeleteEventAPIRequestInput
from services.data_service.api.v3.models.request.event.insert_event_api_request_input import InsertEventAPIRequestInput
from services.data_service.api.v3.models.request.event.update_event_api_request_input import UpdateEventAPIRequestInput
from services.data_service.api.v3.models.request.feed.event_feed_api_request_input import EventFeedAPIRequestInput
from services.data_service.api.v3.models.response.feed.event_feed_api_response import EventFeedAPIResponse
from services.data_service.application.use_cases.event_feed.event_feed_input_boundary import EventFeedInputBoundary
from services.data_service.application.use_cases.event_feed.event_feed_use_case import EventFeedUseCase
from services.data_service.application.use_cases.events.delete_event_by_id_use_case import DeleteEventByIdUseCase
from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase
from services.data_service.application.use_cases.events.models.delete_event_input_boundary import (
    DeleteEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.modify_event_assets_input_boundary import (
    ModifyEventAssetsInputBoundary,
)
from services.data_service.application.use_cases.events.models.update_event_input_boundary import (
    UpdateEventInputBoundary,
)
from services.data_service.application.use_cases.events.modify_event_assets_use_case import ModifyEventAssetsUseCase
from services.data_service.application.use_cases.events.update_event_use_case import UpdateEventUseCase

event_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.EVENT}",
    tags=["event"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@event_router.post(
    EventEndpointRoutes.FEED,
    response_model=EventFeedAPIResponse,
)
async def event_feed_endpoint(
    event_feed_use_case: EventFeedUseCase = Injected(EventFeedUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
    input_boundary: EventFeedInputBoundary = Depends(EventFeedAPIRequestInput.to_input_boundary),
):
    """
    The endpoint allows iteration through user documents which are sorted by document timestamp in descending order.
    The feed is paginated,
     and each page contains a continuation token that can be used to request the next page of results.
    The initial call can define data_type and organization filters,
     which must then stay immutable for subsequent calls with continuation token returned.
    The limit and range parameters can be mutated to fine scope the next request.
    """
    try:
        result = await event_feed_use_case.execute_async(user_uuid=user_uuid, input_boundary=input_boundary)
        return EventFeedAPIResponse(
            items=[EventAPIOutputMapper.map(document=e) for e in result.events],
            continuation_token=ContinuationTokenMarshaller.encode_event_feed_continuation_token(
                result.continuation_token
            ),
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@event_router.post(EventEndpointRoutes.BASE)
async def insert_event_endpoint(
    request_input: InsertEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertEventUseCase = Injected(InsertEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=InsertEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(EventEndpointRoutes.BASE)
async def update_event_endpoint(
    request_input: UpdateEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: UpdateEventUseCase = Injected(UpdateEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=UpdateEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(EventEndpointRoutes.MODIFY_ASSETS)
async def modify_event_assets_endpoint(
    request_input: ModifyEventAssetsAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ModifyEventAssetsUseCase = Injected(ModifyEventAssetsUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=ModifyEventAssetsInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.delete(EventEndpointRoutes.BASE)
async def delete_event_endpoint(
    request_input: DeleteEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: DeleteEventByIdUseCase = Injected(DeleteEventByIdUseCase),
) -> CommonDocumentsIdsResponse:
    try:
        deleted_uuids = await use_case.execute_async(
            boundary=DeleteEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        return CommonDocumentsIdsResponse(document_ids=deleted_uuids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
