import logging
from uuid import UUID

from fastapi import APIRouter, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import NoContentException
from services.data_service.api.constants import DataServicePrefixes, LookupEndpointRoutes
from services.data_service.api.v3.models.request.content.content_lookup_request_input import (
    PostContentLookupRequestInput,
)
from services.data_service.application.use_cases.content.content_lookup_input_boundary import ContentLookupInputBoundary
from services.data_service.application.use_cases.content.content_lookup_use_case import (
    ContentLookupOutputBoundary,
    ContentLookupUseCase,
)

lookup_router = APIRouter(
    prefix=DataServicePrefixes.V3 + DataServicePrefixes.EXPERIMENTAL_PREFIX + DataServicePrefixes.LOOKUP_PREFIX,
    tags=["lookup"],
    responses={
        404: {"description": "Not found"},
    },
)


@lookup_router.post(
    LookupEndpointRoutes.BASE,
    responses={204: {"description": "Couldn't get content from URL", "model": None}},
)
async def post_content_lookup(
    _: UUID = Depends(get_current_uuid),
    body: PostContentLookupRequestInput = Body(...),
    use_case: ContentLookupUseCase = Injected(ContentLookupUseCase),
) -> ContentLookupOutputBoundary:
    try:
        return await use_case.execute(input_boundary=ContentLookupInputBoundary(url=body.url))
    except Exception as error:
        # For now let's make everything 204 on error until we do request url validation
        logging.exception(f"could not lookup url: {body.url}, error: {error}")
        raise NoContentException("could not lookup url")
