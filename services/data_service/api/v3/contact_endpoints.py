from uuid import UUI<PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadRe<PERSON><PERSON>xception,
    DuplicateDocumentsFound,
    IncorrectOperationException,
    NoContentException,
)
from services.data_service.api.constants import ContactEndpointRoutes, DataServicePrefixes
from services.data_service.api.v3.models.request.contact.insert_contact_api_request_input import (
    InsertContactAPIRequestInput,
)
from services.data_service.api.v3.models.request.contact.search_contact_request_input import SearchContactRequestInput
from services.data_service.api.v3.models.request.contact.update_contact_api_request_input import (
    UpdateContactAPIRequestInput,
)
from services.data_service.api.v3.models.response.contact.contact_api_output import ContactAPIOutput
from services.data_service.application.use_cases.contact.archive_contact_use_case import ArchiveContactUseCase
from services.data_service.application.use_cases.contact.insert_contact_use_case import InsertContactUseCase
from services.data_service.application.use_cases.contact.models.insert_contact_input_boundary import (
    InsertContactInputBoundary,
)
from services.data_service.application.use_cases.contact.models.search_contact_input_boundary import (
    SearchContactInputBoundary,
)
from services.data_service.application.use_cases.contact.models.update_contact_input_boundary import (
    UpdateContactInputBoundary,
)
from services.data_service.application.use_cases.contact.search_contact_use_case import SearchContactUseCase
from services.data_service.application.use_cases.contact.update_contact_use_case import UpdateContactUseCase

contact_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.CONTACT}",
    tags=["contact"],
    responses={404: {"description": "Not found"}},
)


@contact_router.post(ContactEndpointRoutes.SEARCH)
async def search_contacts_endpoint(
    use_case: SearchContactUseCase = Injected(SearchContactUseCase),
    input_boundary: SearchContactInputBoundary = Depends(SearchContactRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[ContactAPIOutput]:
    contacts = await use_case.execute_async(input_boundary=input_boundary)
    if not contacts:
        raise NoContentException(message="no matching contacts found")

    output = [ContactAPIOutput.map(model=contact) for contact in contacts]

    return CommonDocumentsResponse[ContactAPIOutput](documents=output)


@contact_router.patch(ContactEndpointRoutes.ARCHIVE)
async def archive_contacts_endpoint(
    contact_ids: list[UUID] = Query(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ArchiveContactUseCase = Injected(ArchiveContactUseCase),
) -> CommonDocumentsResponse[ContactAPIOutput]:
    try:
        contacts = await use_case.execute_async(owner_id=owner_id, contact_ids=contact_ids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[ContactAPIOutput](
        documents=[ContactAPIOutput(**contact.model_dump(by_alias=True)) for contact in contacts]
    )


@contact_router.patch(ContactEndpointRoutes.BASE)
async def update_contacts_endpoint(
    input_boundary: UpdateContactInputBoundary = Depends(UpdateContactAPIRequestInput.to_input_boundary),
    use_case: UpdateContactUseCase = Injected(UpdateContactUseCase),
) -> CommonDocumentsResponse[ContactAPIOutput]:
    try:
        contacts = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="contact duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[ContactAPIOutput](
        documents=[ContactAPIOutput(**contact.model_dump(by_alias=True)) for contact in contacts]
    )


@contact_router.post(ContactEndpointRoutes.BASE)
async def insert_contacts_endpoint(
    input_boundary: InsertContactInputBoundary = Depends(InsertContactAPIRequestInput.to_input_boundary),
    use_case: InsertContactUseCase = Injected(InsertContactUseCase),
) -> CommonDocumentsResponse[ContactAPIOutput]:
    try:
        contacts = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate contact detected. A contact with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    return CommonDocumentsResponse[ContactAPIOutput](
        documents=[ContactAPIOutput(**contact.model_dump(by_alias=True)) for contact in contacts]
    )
