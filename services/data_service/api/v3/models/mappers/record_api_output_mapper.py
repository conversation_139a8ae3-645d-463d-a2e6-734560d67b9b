from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.records.record import Record
from services.data_service.api.output_models.record_api_output import RecordAPIOutput
from services.data_service.type_resolver import TypeResolver


class RecordAPIOutputMapper:
    @staticmethod
    def map(document: Record) -> RecordAPIOutput:
        output_schema = TypeResolver.get_record_api_output(type_id=document.type_id())
        return output_schema(**document.model_dump(by_alias=True) | {DocumentLabels.TYPE: document.type_id()})
