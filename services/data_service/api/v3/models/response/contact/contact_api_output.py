from datetime import date

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.contact import ContactFields
from services.data_service.api.output_models.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.data_service.api.output_models.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)


class ContactAPIOutput(SystemPropertiesDocumentAPIOutput, IdentifiableDocumentAPIOutput):
    last_name: str = <PERSON>(alias=ContactFields.LAST_NAME)
    first_name: str = <PERSON>(alias=ContactFields.FIRST_NAME)
    nickname: str = Field(alias=ContactFields.NICKNAME)
    company: str | None = Field(alias=ContactFields.COMPANY)
    street: str | None = Field(alias=ContactFields.STREET)
    address: str | None = Field(alias=ContactFields.ADDRESS)
    city: str | None = Field(alias=ContactFields.CITY)
    state: str | None = Field(alias=ContactFields.STATE)
    zip: str | None = Field(alias=ContactFields.ZIP)
    note: str | None = Field(alias=ContactFields.NOTE)
    birthday: date = Field(alias=ContactFields.BIRTHDAY)
    relationship: PersonRelationship = Field(alias=ContactFields.RELATIONSHIP)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS)
    archived_at: SerializableAwareDatetime | None
