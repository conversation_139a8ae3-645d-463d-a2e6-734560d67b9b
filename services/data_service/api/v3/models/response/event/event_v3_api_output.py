from typing import Literal, Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.events.core_event import CoreEventCategory, CoreEventFields, CoreEventIdentifier
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.event_group import EventGroupFields, EventGroupIdentifier
from services.base.domain.schemas.events.note import NoteCategory, NoteFields, NoteIdentifier
from services.base.domain.schemas.events.person import PersonFields, PersonIdentifier
from services.base.domain.schemas.events.symptom import (
    SymptomCategory,
    SymptomFields,
    SymptomIdentifier,
    SymptomValueLimits,
)
from services.data_service.api.output_models.events.event_api_output_base import EventAPIOutputBase


class CoreEventAPIOutput(EventAPIOutputBase, CoreEventIdentifier):
    type: Literal[DataType.CoreEvent] = Field(alias=CoreEventFields.TYPE)
    category: CoreEventCategory = Field(alias=CoreEventFields.CATEGORY)
    rating: int | None = Field(
        alias=CoreEventFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )


class NoteAPIOutput(EventAPIOutputBase, NoteIdentifier):
    type: Literal[DataType.Note] = Field(alias=NoteFields.TYPE)
    category: NoteCategory = Field(alias=NoteFields.CATEGORY)


class SymptomAPIOutput(EventAPIOutputBase, SymptomIdentifier):
    type: Literal[DataType.Symptom] = Field(alias=SymptomFields.TYPE)
    category: SymptomCategory = Field(alias=SymptomFields.CATEGORY)
    rating: int | None = Field(
        alias=SymptomFields.RATING,
        ge=SymptomValueLimits.SYMPTOM_RATING_MINIMUM_VALUE,
        le=SymptomValueLimits.SYMPTOM_RATING_MAXIMUM_VALUE,
    )
    body_parts: Sequence[BodyParts] = Field(alias=SymptomFields.BODY_PARTS)


class EventGroupAPIOutput(EventAPIOutputBase, EventGroupIdentifier):
    type: Literal[DataType.EventGroup] = Field(alias=EventGroupFields.TYPE)


class PersonAPIOutput(EventAPIOutputBase, PersonIdentifier):
    type: Literal[DataType.Person] = Field(alias=PersonFields.TYPE)
    category: PersonRelationship = Field(alias=PersonFields.CATEGORY)
    rating: int | None = Field(
        alias=PersonFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    contact_id: UUID = Field(alias=PersonFields.CONTACT_ID)
