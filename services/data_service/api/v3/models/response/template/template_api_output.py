from typing import Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.templates.event_template import TypedTemplatePayloads
from services.base.domain.schemas.templates.template import TemplateFields
from services.data_service.api.output_models.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.data_service.api.output_models.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)


class TemplateAPIOutputBase(SystemPropertiesDocumentAPIOutput, IdentifiableDocumentAPIOutput):
    name: str = Field(alias=TemplateFields.NAME, min_length=1)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)
    archived_at: SerializableAwareDatetime | None = Field(alias=TemplateFields.ARCHIVED_AT)


class EventTemplateAPIOutput(TemplateAPIOutputBase):
    document_name: str = Field(alias=TemplateFields.DOCUMENT_NAME, min_length=1)
    document: TypedTemplatePayloads = Field(alias=TemplateFields.DOCUMENT)
    document_type: EventType = Field(alias=TemplateFields.DOCUMENT_TYPE)


class GroupTemplateAPIOutput(TemplateAPIOutputBase):
    template_ids: Sequence[UUID] = Field(alias=TemplateFields.TEMPLATE_IDS)


TemplateAPIOutput = EventTemplateAPIOutput | GroupTemplateAPIOutput
