from typing import Literal
from uuid import UUID

from pydantic import Field, field_validator

from services.base.domain.annotated_types import (
    NonEmptyStr,
    SerializableAwareDatetime,
    UniqueSequenceStr,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.priority import Priority
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.events.plan import PlanFields, PlanMetadata, PlanStreak, PlanValueLimits
from services.data_service.api.output_models.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.data_service.api.output_models.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)


class PlanAPIOutput(SystemPropertiesDocumentAPIOutput, IdentifiableDocumentAPIOutput):
    type: Literal[DataType.Plan] = Field(alias=DocumentLabels.TYPE)
    metadata: PlanMetadata = Field(alias=PlanFields.METADATA)
    name: NonEmptyStr = Field(alias=PlanFields.NAME, min_length=1, max_length=PlanValueLimits.MAX_NAME_LENGTH)
    template_id: UUID = Field(alias=PlanFields.TEMPLATE_ID)
    next_scheduled_at: SerializableAwareDatetime = Field(alias=PlanFields.NEXT_SCHEDULED_AT)
    first_completed_at: SerializableAwareDatetime | None = Field(alias=PlanFields.FIRST_COMPLETED_AT)
    archived_at: SerializableAwareDatetime | None = Field(alias=PlanFields.ARCHIVED_AT)
    is_urgent: bool = Field(alias=PlanFields.IS_URGENT)
    is_confirmation_required: bool = Field(alias=PlanFields.IS_CONFIRMATION_REQUIRED)
    is_absolute_schedule: bool = Field(alias=PlanFields.IS_ABSOLUTE_SCHEDULE)
    streak: PlanStreak = Field(alias=PlanFields.STREAK)
    recurrence: NonEmptyStr | None = Field(alias=PlanFields.RECURRENCE, min_length=1)
    note: NonEmptyStr | None = Field(alias=PlanFields.NOTE, min_length=1)
    priority: Priority = Field(alias=PlanFields.PRIORITY)
    prompt: NonEmptyStr = Field(alias=PlanFields.PROMPT, min_length=1)
    current_completed: int = Field(alias=PlanFields.CURRENT_COMPLETED, ge=0)
    max_completed: int | None = Field(alias=PlanFields.MAX_COMPLETED, ge=0)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)

    @field_validator(PlanFields.RECURRENCE, mode="before")
    def validate_recurrence(cls, recurrence: CustomRRule | str | None) -> str | None:
        return str(recurrence) if recurrence else None
