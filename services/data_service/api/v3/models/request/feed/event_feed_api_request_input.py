from __future__ import annotations

from typing import Optional

from fastapi import Body, Query
from pydantic import Field

from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.application.database.models.sorts import Sort
from services.base.domain.annotated_types import NonEmptyStr
from services.data_service.api.queries.event_query_api import EventQueryAPI
from services.data_service.api.v1.continuation_token_marshaller import ContinuationTokenMarshaller
from services.data_service.application.models.event_feed_continuation_token import EventFeedContinuationToken
from services.data_service.application.use_cases.event_feed.event_feed_input_boundary import EventFeedInputBoundary


class EventFeedAPIRequestInput(EventQueryAPI):
    sort: SortRequestInput = Field(default=SortRequestInput())

    @staticmethod
    def to_input_boundary(
        request_input: EventFeedAPIRequestInput = Body(...),
        limit: int = Query(default=100, gt=0, le=10000),
        continuation_token: Optional[NonEmptyStr] = Query(default=None, min_length=1),
    ) -> EventFeedInputBoundary:
        return EventFeedInputBoundary(
            limit=limit,
            query=request_input.to_query(),
            sort=Sort(name=request_input.sort.field_name, order=request_input.sort.order),
            continuation_token=request_input._decode_continuation_token(continuation_token=continuation_token),
        )

    @staticmethod
    def _decode_continuation_token(continuation_token: Optional[str]) -> EventFeedContinuationToken | None:
        if continuation_token:
            return ContinuationTokenMarshaller.decode_event_feed_continuation_token(continuation_token)
        else:
            return None
