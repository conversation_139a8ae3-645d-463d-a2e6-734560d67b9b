from __future__ import annotations

from typing import Sequence
from uuid import UUID

from fastapi import Body, Depends
from pydantic import Field

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.plans.models.complete_plans_input_boundary import (
    CompletePlanInput,
    CompletePlansInputBoundary,
)


class CompletePlansAPIRequestInput(BaseDataModel):
    documents: Sequence[CompletePlanInput] = Field(min_length=1)

    @staticmethod
    def to_input_boundary(
        body: CompletePlansAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> CompletePlansInputBoundary:
        return CompletePlansInputBoundary(
            documents=body.documents,
            owner_id=owner_id,
        )
