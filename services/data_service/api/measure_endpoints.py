from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.common_data_response import CommonDataResponse
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.data_service.api.constants import DataServicePrefixes, MeasureEndpointRoutes
from services.data_service.api.utils.get_common_fetch_input import (
    CommonFetchInputModelOptionalInterval,
    get_common_fetch_input_optional_interval,
)
from services.data_service.application.use_cases.aggregate_heart_rate_use_case import (
    AggregateHeartRateUseCase,
    AggregateHeartRateUseCaseOutputItem,
)
from services.data_service.application.use_cases.aggregate_resting_heart_rate_use_case import (
    AggregateRestingHeartRateUseCase,
    AggregateRestingHeartRateUseCaseOutputItem,
)
from services.data_service.application.use_cases.list_heart_rate_use_case import (
    ListHeartRateOutputItem,
    ListHeartRateUseCase,
)
from services.data_service.application.use_cases.list_resting_heart_rate_use_case import (
    ListRestingHeartRateOutputItem,
    ListRestingHeartRateUseCase,
)
from services.data_service.constants import DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES

measure_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.MEASURE_PREFIX}",
    tags=["measure"],
    responses={404: {"description": "Not found"}},
)


@measure_router.get(
    MeasureEndpointRoutes.HEART_RATE,
    response_model=CommonDataResponse[ListHeartRateOutputItem]
    | CommonDataResponse[AggregateHeartRateUseCaseOutputItem],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
)
async def heart_rate_endpoint(
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    heart_rate_detail_use_case: ListHeartRateUseCase = Injected(ListHeartRateUseCase),
    heart_rate_aggregation_use_case: AggregateHeartRateUseCase = Injected(AggregateHeartRateUseCase),
):
    has_interval = isinstance(common_input.time_input, TimeInput)
    use_case = heart_rate_aggregation_use_case if has_interval else heart_rate_detail_use_case
    output_boundary = AggregateHeartRateUseCaseOutputItem if has_interval else ListHeartRateOutputItem

    output = await use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,  # pyright: ignore
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[output_boundary](
        Values=output.results,
        Status=status,
    )


@measure_router.get(
    MeasureEndpointRoutes.RESTING_HEART_RATE,
    response_model=CommonDataResponse[ListRestingHeartRateOutputItem]
    | CommonDataResponse[AggregateRestingHeartRateUseCaseOutputItem],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
)
async def resting_heart_rate_endpoint(
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    rhr_aggregation_use_case: AggregateRestingHeartRateUseCase = Injected(AggregateRestingHeartRateUseCase),
    rhr_detail_use_case: ListRestingHeartRateUseCase = Injected(ListRestingHeartRateUseCase),
):
    has_interval = isinstance(common_input.time_input, TimeInput)
    use_case = rhr_aggregation_use_case if has_interval else rhr_detail_use_case
    output_boundary = AggregateRestingHeartRateUseCaseOutputItem if has_interval else ListRestingHeartRateOutputItem

    output = await use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,  # pyright: ignore
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[output_boundary](Values=output.results, Status=status)
