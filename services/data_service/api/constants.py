from dataclasses import dataclass


@dataclass(frozen=True)
class DataServicePrefixes:
    VERSION1_PREFIX = "/api/v1.0"
    VERSION2_PREFIX = "/api/v0.2"
    V3 = "/v3"

    AGGREGATION = "/aggs"
    ANALYSE = "/analyse"
    AI = "/ai"
    ASSET = "/asset"
    DOCUMENT = "/document"
    RECORD = "record"
    EVENT = "/event"
    ACTIVITY_PREFIX = "/activity"
    LOADING = "/loading"
    ASYNCHRONOUS_LOADING = "/asynchronous_loading"
    FETCH_PREFIX = "/fetch"
    MEASURE_PREFIX = "/measure"
    DIARY_PREFIX = "/diary"
    DATA_SUMMARY_PREFIX = "/summary"
    ENVIRONMENT_PREFIX = "/environment"
    LOCATION_PREFIX = "/location"
    DELETE_PREFIX = "/delete"
    UPDATE_PREFIX = "/update"
    PLAN_PREFIX = "/plan"
    USE_CASE_PREFIX = "/use_case"
    TEMPLATE_PREFIX = "/template"
    CONTACT = "/contact"
    EXTENSION_PREFIX = "/extension"
    EXTENSION_RESULTS_PREFIX = "/extension/results"
    EXTENSION_DIRECTORY_PREFIX = "/extension/directory"
    EXTENSION_PROVIDERS_PREFIX = "/extension/providers"

    EXPERIMENTAL_PREFIX = "/x"
    LOOKUP_PREFIX = "/lookup"  # @REVIEW: What ought to be the name for this?


@dataclass(frozen=True)
class ActivityEndpointRoutes:
    SLEEP = "/sleep/"
    STEPS = "/steps/"
    INTERACT_SOCIAL_TOTALS_IN_TIME = "/interact_social_totals_in_time/"
    INTERACT_SOCIAL_TOTALS_PER_PERSON = "/interact_social_totals_per_person/"
    INTERACT_SOCIAL_TOTALS_PER_PERSON_GROUPED = "/interact_social_totals_per_person_grouped/"
    SHOPPING_ACTIVITY = "/shopping_activity/"


@dataclass(frozen=True)
class AssetEndpointRoutes:
    BY_ID = "/by_id/"
    URL = "/url/"


@dataclass(frozen=True)
class MeasureEndpointRoutes:
    HEART_RATE = "/heart_rate/"
    RESTING_HEART_RATE = "/resting_heart_rate/"


@dataclass(frozen=True)
class DiaryEndpointRoutes:
    EVENTS = "/events/"
    EVENTS_UNIQUE = "/events/unique/"
    RATINGS = "/ratings/"
    NOTES = "/notes/"


@dataclass(frozen=True)
class EnvironmentEndpointRoutes:
    AIR_QUALITY = "/air_quality/"
    FORECAST_AIR_QUALITY = "/forecast/air_quality/"
    WEATHER = "/weather/"
    FORECAST_WEATHER = "/forecast/weather/"
    POLLEN = "/pollen/"
    FORECAST_POLLEN = "/forecast/pollen/"

    AIR_QUALITY_BY_SPACETIME = "/air_quality/spacetime/"
    WEATHER_BY_SPACETIME = "/weather/spacetime/"
    POLLEN_BY_SPACETIME = "/pollen/spacetime/"


@dataclass(frozen=True)
class LocationEndpointRoutes:
    HISTORY = "/history/"
    MOVEMENT = "/movement/"
    PLACE = "/place/"


@dataclass(frozen=True)
class LoadingEndpointRoutes:
    TEMP_PLAN = "/temp/plan/"


@dataclass(frozen=True)
class RecordEndpointRoutes:
    BASE = "/"


@dataclass(frozen=True)
class EventEndpointRoutes:
    BASE = "/"
    FEED = "/feed/"
    MODIFY_ASSETS = "/modify_assets/"


@dataclass(frozen=True)
class DataCrudEndpointRoutes:
    BASE = "/"
    BLOOD_PRESSURE = "/blood_pressure/"
    BLOOD_SUGAR = "/blood_sugar/"
    BODY_TEMPERATURE = "/body_temperature/"
    EVENT = "/event/"
    HEART_RATE = "/heart_rate/"
    LOCATION = "/location/"
    NOTE = "/note/"
    PULSE_OXYGEN = "/pulse_oxygen/"
    RATING = "/rating/"
    RESTING_HEART_RATE = "/resting_heart_rate/"
    SLEEP = "/sleep/"
    STEPS = "/steps/"
    WEIGHT = "/weight/"


@dataclass(frozen=True)
class DataSummaryEndpointRoutes:
    USER_DATA = "/user_data/"
    TAG_COUNT = "/tag_count/"
    STATISTICS = "/statistics/"
    BODY_PARTS = "/body_parts/"


@dataclass(frozen=True)
class AggregationEndpointRoutes:
    BASE = "/"
    FREQUENCY_DISTRIBUTION = "/frequency_distribution/"
    DATE_HISTOGRAM = "/date_histogram/"
    CALENDAR_FREQUENCY_DISTRIBUTION = "/calendar_frequency_distribution/"
    CALENDAR_HISTOGRAM_AGGREGATION = "/calendar_histogram_aggregation/"


@dataclass(frozen=True)
class AnalyseEndpointRoutes:
    BASE = "/"
    TREND_DETECT = "/trend_detect/"
    CORRELATE_EVENT = "/correlate/event/"
    CORRELATE_EVENT_SUGGEST_PARAMETERS = "/correlate/event/suggest/parameters"


@dataclass(frozen=True)
class AIEndpointRoutes:
    BASE = "/"
    SUGGEST_EVENT = "/suggest/event/"


@dataclass(frozen=True)
class DeleteEndpointRoutes:
    BY_ID = "/by_id/"
    BY_FILTER = "/by_filter/"


@dataclass(frozen=True)
class UpdateEndpointRoutes:
    BY_ID = "/by_id/"


@dataclass(frozen=True)
class PlanEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    COMPLETE = "/complete/"
    ARCHIVE = "/archive/"
    TEMP = "/temp/"
    TEMP_ARCHIVE = "/temp/archive/"


@dataclass(frozen=True)
class UseCaseEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    ARCHIVE = "/archive/"


@dataclass(frozen=True)
class ContactEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    ARCHIVE = "/archive/"


@dataclass(frozen=True)
class TemplateRoutes:
    BASE = "/"
    SEARCH = "/search/"
    ARCHIVE = "/archive/"


@dataclass(frozen=True)
class FetchEndpointRoutes:
    BY_ID = "/by_id/"


@dataclass(frozen=True)
class ExtensionsEndpointRoutes:
    LIST_RUNS = "/runs/"
    LIST_RESULTS = "/results/"


@dataclass(frozen=True)
class ExtensionDirectoryEndpointRoutes:
    BASE = "/"
    LIST = "/list/"
    SUBSCRIBE = "/subscribe/"
    UNSUBSCRIBE = "/unsubscribe/"


@dataclass(frozen=True)
class ExtensionProvidersEndpointRoutes:
    BASE = "/"
    LIST = "/list/"


@dataclass(frozen=True)
class LookupEndpointRoutes:
    BASE = "/"


@dataclass(frozen=True)
class DocumentEndpointRoutes:
    BASE = "/"
    FEED = "/feed/"
    ALL_DATA = "/all_data/"
    BY_QUERY = "/by_query/"
