from uuid import UUI<PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, <PERSON>, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import NoContentException
from services.base.domain.enums.data_types import DataType
from services.data_service.api.constants import DataCrudEndpointRoutes, DataServicePrefixes
from services.data_service.api.output_models.diary_event_api_output import DiaryEventAPIOutput
from services.data_service.api.output_models.heart_rate_api_output import HeartRateAPIOutput
from services.data_service.api.output_models.location_api_output import LocationAPIOutput
from services.data_service.api.output_models.resting_heart_rate_api_output import RestingHeartRateAPIOutput
from services.data_service.api.output_models.sleep_api_output import SleepAPIOutput
from services.data_service.api.output_models.steps_api_output import StepsAPIOutput
from services.data_service.api.request_models.load_event_api_request_input import LoadDiaryEventsAPIRequestInput
from services.data_service.api.request_models.load_heart_rate_api_request_input import LoadHeartRateAPIRequestInput
from services.data_service.api.request_models.load_location_api_request_input import LoadLocationAPIRequestInput
from services.data_service.api.request_models.load_resting_heart_rate_api_request_input import (
    LoadRestingHeartRateAPIRequestInput,
)
from services.data_service.api.request_models.load_sleep_api_request_input import LoadSleepAPIRequestInput
from services.data_service.api.request_models.load_steps_api_request_input import LoadStepsAPIRequestInput
from services.data_service.application.use_cases.loading.diary_event.load_diary_events_use_case import (
    LoadDiaryEventsUseCase,
)
from services.data_service.application.use_cases.loading.diary_event.models.load_diary_events_input_boundary import (
    LoadDiaryEventsInputBoundary,
)
from services.data_service.application.use_cases.loading.heart_rate.load_heart_rate_use_case import LoadHeartRateUseCase
from services.data_service.application.use_cases.loading.heart_rate.models.load_heart_rate_input_boundary import (
    LoadHeartRateInputBoundary,
)
from services.data_service.application.use_cases.loading.location.load_location_use_case import LoadLocationUseCase
from services.data_service.application.use_cases.loading.location.models.load_location_input_boundary import (
    LoadLocationInputBoundary,
)
from services.data_service.application.use_cases.loading.resting_heart_rate.load_resting_heart_rate_use_case import (
    LoadRestingHeartRateUseCase,
)
from services.data_service.application.use_cases.loading.resting_heart_rate.models.load_resting_heart_rate_input_boundary import (
    LoadRestingHeartRateInputBoundary,
)
from services.data_service.application.use_cases.loading.sleep.load_sleep_use_case import LoadSleepUseCase
from services.data_service.application.use_cases.loading.sleep.models.load_sleep_input_boundary import (
    LoadSleepInputBoundary,
)
from services.data_service.application.use_cases.loading.steps.load_steps_use_case import LoadStepsUseCase
from services.data_service.application.use_cases.loading.steps.models.load_steps_input_boundary import (
    LoadStepsInputBoundary,
)

data_crud_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION1_PREFIX}",
    tags=["data"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@data_crud_router.post(DataCrudEndpointRoutes.EVENT)
async def load_event_endpoint(
    input_data: LoadDiaryEventsAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: LoadDiaryEventsUseCase = Injected(LoadDiaryEventsUseCase),
) -> CommonDocumentsResponse[DiaryEventAPIOutput]:
    result = await use_case.execute_async(
        input_data=LoadDiaryEventsInputBoundary.map(model=input_data), user_uuid=user_uuid
    )
    if len(result) == 0:
        raise NoContentException(message="No diary events loaded")

    return CommonDocumentsResponse[DiaryEventAPIOutput](
        documents=[DiaryEventAPIOutput(**item.model_dump(by_alias=True)) for item in result]
    )


@data_crud_router.post(DataCrudEndpointRoutes.LOCATION)
async def load_location_endpoint(
    input_data: LoadLocationAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: LoadLocationUseCase = Injected(LoadLocationUseCase),
) -> CommonDocumentsResponse[LocationAPIOutput]:
    locations = await use_case.execute_async(
        input_data=LoadLocationInputBoundary(documents=input_data.documents, metadata=input_data.metadata),
        user_uuid=user_uuid,
    )

    locations_api_outputs = [
        LocationAPIOutput(**loc.model_dump(by_alias=True), type=DataType.Location) for loc in locations
    ]
    if len(locations_api_outputs) == 0:
        raise NoContentException(message="No locations loaded")

    return CommonDocumentsResponse[LocationAPIOutput](documents=locations_api_outputs)


@data_crud_router.post(DataCrudEndpointRoutes.SLEEP)
async def load_sleep_data_endpoint(
    input_data: LoadSleepAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: LoadSleepUseCase = Injected(LoadSleepUseCase),
) -> CommonDocumentsResponse[SleepAPIOutput]:
    result = await use_case.execute_async(input_data=LoadSleepInputBoundary.map(model=input_data), user_uuid=user_uuid)
    if len(result) == 0:
        raise NoContentException(message="No sleep data loaded")

    return CommonDocumentsResponse[SleepAPIOutput](
        documents=[SleepAPIOutput(**item.model_dump(by_alias=True), type=DataType.Sleep) for item in result]
    )


@data_crud_router.post(DataCrudEndpointRoutes.HEART_RATE)
async def load_heart_rate_endpoint(
    input_data: LoadHeartRateAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: LoadHeartRateUseCase = Injected(LoadHeartRateUseCase),
) -> CommonDocumentsResponse[HeartRateAPIOutput]:
    result = await use_case.execute_async(
        input_data=LoadHeartRateInputBoundary.map(model=input_data), user_uuid=user_uuid
    )
    if len(result) == 0:
        raise NoContentException(message="No heart rate data loaded")

    return CommonDocumentsResponse[HeartRateAPIOutput](
        documents=[HeartRateAPIOutput(**item.model_dump(by_alias=True), type=DataType.HeartRate) for item in result]
    )


@data_crud_router.post(DataCrudEndpointRoutes.RESTING_HEART_RATE)
async def load_resting_heart_rate_endpoint(
    input_data: LoadRestingHeartRateAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: LoadRestingHeartRateUseCase = Injected(LoadRestingHeartRateUseCase),
) -> CommonDocumentsResponse[RestingHeartRateAPIOutput]:
    result = await use_case.execute_async(
        input_data=LoadRestingHeartRateInputBoundary.map(model=input_data),
        user_uuid=user_uuid,
    )
    if len(result) == 0:
        raise NoContentException(message="No resting heart rate data loaded")

    return CommonDocumentsResponse[RestingHeartRateAPIOutput](
        documents=[
            RestingHeartRateAPIOutput(**item.model_dump(by_alias=True), type=DataType.RestingHeartRate)
            for item in result
        ]
    )


@data_crud_router.post(DataCrudEndpointRoutes.STEPS)
async def load_steps_endpoint(
    input_data: LoadStepsAPIRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: LoadStepsUseCase = Injected(LoadStepsUseCase),
) -> CommonDocumentsResponse[StepsAPIOutput]:
    result = await use_case.execute_async(
        input_data=LoadStepsInputBoundary.map(model=input_data),
        user_uuid=user_uuid,
    )
    if len(result) == 0:
        raise NoContentException(message="No steps data loaded")

    return CommonDocumentsResponse[StepsAPIOutput](
        documents=[StepsAPIOutput(**item.model_dump(by_alias=True), type=DataType.Steps) for item in result]
    )
