from uuid import UUI<PERSON>

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import BadRequestException, IncorrectOperationException
from services.base.application.utils.encoders import encode_base_64
from services.data_service.api.constants import (
    DataServicePrefixes,
    ExtensionsEndpointRoutes,
)
from services.data_service.api.output_models.extensions.extension_results_api_output import (
    ExtensionResultsAPIOutput,
    ExtensionRunsAPIOutput,
)
from services.data_service.api.request_models.extensions.list_extension_request_input import (
    ListExtensionResultsRequestInput,
    ListExtensionRunRequestInput,
    get_list_extension_results_request_input,
    get_list_extension_runs_request_input,
)
from services.data_service.api.response_models.extensions.list_extension_results_response import (
    ListExtensionResultsResponse,
    ListExtensionRunsResponse,
)
from services.data_service.application.use_cases.extensions.list_extension_results_use_case import (
    ListExtensionResultsUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_runs_use_case import (
    ListExtensionRunsUseCase,
)

extension_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION1_PREFIX}{DataServicePrefixes.EXTENSION_PREFIX}",
    tags=["extensions"],
    responses={404: {"description": "Not found"}},
)


@extension_router.get(ExtensionsEndpointRoutes.LIST_RUNS)
async def list_extension_runs_endpoint(
    list_extension_runs_use_case: ListExtensionRunsUseCase = Injected(ListExtensionRunsUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
    request_input: ListExtensionRunRequestInput = Depends(get_list_extension_runs_request_input),
) -> ListExtensionRunsResponse:
    response = await list_extension_runs_use_case.execute_async(
        extension_ids=request_input.extension_ids,
        range_filter=request_input.range_filter,
        sort=request_input.sort,
        user_uuid=user_uuid,
        limit=request_input.limit,
        continuation_token=request_input.continuation_token,
    )

    return ListExtensionRunsResponse(
        continuation_token=encode_base_64(data=response.continuation_token.model_dump_json(by_alias=True)),
        items=[ExtensionRunsAPIOutput.map(model=result) for result in response.extension_runs],
    )


@extension_router.get(ExtensionsEndpointRoutes.LIST_RESULTS)
async def list_extension_results_endpoint(
    list_extension_result_use_case: ListExtensionResultsUseCase = Injected(ListExtensionResultsUseCase),
    request_input: ListExtensionResultsRequestInput = Depends(get_list_extension_results_request_input),
    _: UUID = Depends(get_current_uuid),
) -> ListExtensionResultsResponse:
    try:
        response = await list_extension_result_use_case.execute_async(run_id=request_input.run_id)

        return ListExtensionResultsResponse(
            items=[ExtensionResultsAPIOutput.map(model=result) for result in response],
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
