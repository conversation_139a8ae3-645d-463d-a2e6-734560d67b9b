from typing import Sequence

from pydantic import Field

from services.base.api.query.boolean_query_api import CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPIAnnotated
from services.base.api.query.mapper.query_api_mapper import QueryAPIMapper
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel


class EventTypedQueryAPI(BaseDataModel):
    types: Sequence[EventType] = Field(min_length=1)
    query: LeafQueryAPIAnnotated | CompoundBooleanQueryAPI | None = Field(default=None)

    def to_type_query(self) -> TypeQuery:
        converted_query = QueryAPIMapper.map(query=self.query)
        return TypeQuery(query=converted_query, domain_types=[ft.to_domain_model() for ft in self.types])


class EventQueryAPI(BaseDataModel):
    queries: Sequence[EventTypedQueryAPI] = Field(default_factory=list)

    def to_query(self) -> Query:
        if self.queries:
            return Query(type_queries=[q.to_type_query() for q in self.queries])
        else:
            typed_query = TypeQuery(domain_types=[et.to_domain_model() for et in EventType], query=None)
            return Query(type_queries=[typed_query])
