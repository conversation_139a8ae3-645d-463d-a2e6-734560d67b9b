import logging
from typing import Dict, Sequence
from uuid import UUID

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import BadRequestException, InvalidDatabaseRequestException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.data_service.api.constants import DataServicePrefixes, UpdateEndpointRoutes
from services.data_service.api.utils.get_schema_model import get_data_type_schema_model
from services.data_service.application.enums.updatable_data_type import UpdatableDataType
from services.data_service.application.use_cases.by_id.update_by_id_use_case import UpdateByIdUseCase

update_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.UPDATE_PREFIX}",
    tags=["modify"],
    responses={404: {"description": "Not found"}},
)


@update_router.patch(
    UpdateEndpointRoutes.BY_ID,
    responses={200: {"model": Dict}},
)
async def update_by_id_endpoint(
    data_type: UpdatableDataType,
    entries: Sequence[DeprEventModel] = Depends(get_data_type_schema_model),
    user_uuid: UUID = Depends(get_current_uuid),
    target_use_case: UpdateByIdUseCase = Injected(UpdateByIdUseCase),
):
    """Updates the given entry that matches one of supported DataType"""
    try:
        updated_entries = await target_use_case.execute_async(
            data_schema=data_type.to_domain_model(),
            user_uuid=user_uuid,
            updated_entries=entries,
        )
    except InvalidDatabaseRequestException as err:
        logging.warning(f"Unable to update entry: {entries[0]}, id: {entries[0].doc_id} err: {err}")
        raise BadRequestException(message="Unable to update entry.") from err

    return [
        entry.model_dump(by_alias=True, exclude={DocumentLabels.METADATA: {DocumentLabels.USER_UUID}})
        for entry in updated_entries
    ][0]
