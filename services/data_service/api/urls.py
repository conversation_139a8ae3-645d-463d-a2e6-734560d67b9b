from dataclasses import dataclass

from services.data_service.api.activity_endpoints import activity_router
from services.data_service.api.constants import (
    ActivityEndpointRoutes,
    AggregationEndpointRoutes,
    AIEndpointRoutes,
    AnalyseEndpointRoutes,
    AssetEndpointRoutes,
    ContactEndpointRoutes,
    DataCrudEndpointRoutes,
    DataSummaryEndpointRoutes,
    DeleteEndpointRoutes,
    DiaryEndpointRoutes,
    DocumentEndpointRoutes,
    EnvironmentEndpointRoutes,
    EventEndpointRoutes,
    ExtensionDirectoryEndpointRoutes,
    ExtensionProvidersEndpointRoutes,
    ExtensionsEndpointRoutes,
    LocationEndpointRoutes,
    LookupEndpointRoutes,
    MeasureEndpointRoutes,
    PlanEndpointRoutes,
    RecordEndpointRoutes,
    TemplateRoutes,
    UpdateEndpointRoutes,
    UseCaseEndpointRoutes,
)
from services.data_service.api.data_summary_endpoints import summary_router
from services.data_service.api.delete_endpoints import delete_router
from services.data_service.api.diary_endpoints import diary_router
from services.data_service.api.environment_v2_endpoints import environment_router as environment_router_v2
from services.data_service.api.location_endpoints import location_router
from services.data_service.api.measure_endpoints import measure_router
from services.data_service.api.temp_plan_endpoints import temp_plan_router
from services.data_service.api.update_endpoints import update_router
from services.data_service.api.v1.data_crud_endpoints import data_crud_router
from services.data_service.api.v1.extensions.extension_directory_endpoints import extension_directory_router
from services.data_service.api.v1.extensions.extension_providers_endpoints import extension_providers_router
from services.data_service.api.v1.extensions.extension_results_endpoints import extension_router
from services.data_service.api.v3.aggregation_endpoints import aggregation_router
from services.data_service.api.v3.ai_endpoints import ai_router
from services.data_service.api.v3.analyse_endpoints import analyse_router
from services.data_service.api.v3.assets_endpoints import assets_router
from services.data_service.api.v3.contact_endpoints import contact_router
from services.data_service.api.v3.document_endpoints import document_router
from services.data_service.api.v3.event_endpoints import event_router
from services.data_service.api.v3.lookup_endpoints import lookup_router
from services.data_service.api.v3.plan_endpoints import plan_router
from services.data_service.api.v3.record_endpoints import record_router
from services.data_service.api.v3.template_endpoints import template_router
from services.data_service.api.v3.use_case_endpoints import use_case_router


@dataclass(frozen=True)
class ActivityEndpointUrls:
    SLEEP = f"{activity_router.prefix}{ActivityEndpointRoutes.SLEEP}"
    STEPS = f"{activity_router.prefix}{ActivityEndpointRoutes.STEPS}"
    INTERACT_SOCIAL_TOTALS_IN_TIME = f"{activity_router.prefix}{ActivityEndpointRoutes.INTERACT_SOCIAL_TOTALS_IN_TIME}"
    INTERACT_SOCIAL_TOTALS_PER_PERSON = (
        f"{activity_router.prefix}{ActivityEndpointRoutes.INTERACT_SOCIAL_TOTALS_PER_PERSON}"
    )
    INTERACT_SOCIAL_TOTALS_PER_PERSON_GROUPED = (
        f"{activity_router.prefix}{ActivityEndpointRoutes.INTERACT_SOCIAL_TOTALS_PER_PERSON_GROUPED}"
    )
    SHOPPING_ACTIVITY = f"{activity_router.prefix}{ActivityEndpointRoutes.SHOPPING_ACTIVITY}"


@dataclass(frozen=True)
class AssetsEndpointUrls:
    BY_ID = f"{assets_router.prefix}{AssetEndpointRoutes.BY_ID}"
    URL = f"{assets_router.prefix}{AssetEndpointRoutes.URL}"


@dataclass(frozen=True)
class MeasureEndpointUrls:
    HEART_RATE = f"{measure_router.prefix}{MeasureEndpointRoutes.HEART_RATE}"
    RESTING_HEART_RATE = f"{measure_router.prefix}{MeasureEndpointRoutes.RESTING_HEART_RATE}"


@dataclass(frozen=True)
class DiaryEndpointUrls:
    EVENTS = f"{diary_router.prefix}{DiaryEndpointRoutes.EVENTS}"
    EVENTS_UNIQUE = f"{diary_router.prefix}{DiaryEndpointRoutes.EVENTS_UNIQUE}"
    RATINGS = f"{diary_router.prefix}{DiaryEndpointRoutes.RATINGS}"
    NOTES = f"{diary_router.prefix}{DiaryEndpointRoutes.NOTES}"


@dataclass(frozen=True)
class UpdateEndpointUrls:
    BY_ID = f"{update_router.prefix}{UpdateEndpointRoutes.BY_ID}"


@dataclass(frozen=True)
class DeleteEndpointUrls:
    BY_ID = f"{delete_router.prefix}{DeleteEndpointRoutes.BY_ID}"
    BY_FILTER = f"{delete_router.prefix}{DeleteEndpointRoutes.BY_FILTER}"


@dataclass(frozen=True)
class EnvironmentV2EndpointUrls:
    AIR_QUALITY = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.AIR_QUALITY}"
    POLLEN = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.POLLEN}"
    WEATHER = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.WEATHER}"

    FORECAST_AIR_QUALITY = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.FORECAST_AIR_QUALITY}"
    FORECAST_WEATHER = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.FORECAST_WEATHER}"
    FORECAST_POLLEN = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.FORECAST_POLLEN}"

    AIR_QUALITY_BY_SPACETIME = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.AIR_QUALITY_BY_SPACETIME}"
    WEATHER_BY_SPACETIME = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.WEATHER_BY_SPACETIME}"
    POLLEN_BY_SPACETIME = f"{environment_router_v2.prefix}{EnvironmentEndpointRoutes.POLLEN_BY_SPACETIME}"


@dataclass(frozen=True)
class PlanEndpointUrls:
    BASE = f"{plan_router.prefix}{PlanEndpointRoutes.BASE}"
    SEARCH = f"{plan_router.prefix}{PlanEndpointRoutes.SEARCH}"
    ARCHIVE = f"{plan_router.prefix}{PlanEndpointRoutes.ARCHIVE}"
    COMPLETE = f"{plan_router.prefix}{PlanEndpointRoutes.COMPLETE}"
    TEMP = f"{temp_plan_router.prefix}{PlanEndpointRoutes.TEMP}"
    TEMP_ARCHIVE = f"{temp_plan_router.prefix}{PlanEndpointRoutes.TEMP_ARCHIVE}"


@dataclass(frozen=True)
class TemplateEndpointUrls:
    BASE = f"{template_router.prefix}{TemplateRoutes.BASE}"
    SEARCH = f"{template_router.prefix}{TemplateRoutes.SEARCH}"
    ARCHIVE = f"{template_router.prefix}{TemplateRoutes.ARCHIVE}"


class UseCaseEndpointUrls:
    BASE = f"{use_case_router.prefix}{UseCaseEndpointRoutes.BASE}"
    SEARCH = f"{use_case_router.prefix}{UseCaseEndpointRoutes.SEARCH}"
    ARCHIVE = f"{use_case_router.prefix}{UseCaseEndpointRoutes.ARCHIVE}"


@dataclass(frozen=True)
class ContactEndpointUrls:
    BASE = f"{contact_router.prefix}{ContactEndpointRoutes.BASE}"
    SEARCH = f"{contact_router.prefix}{ContactEndpointRoutes.SEARCH}"
    ARCHIVE = f"{contact_router.prefix}{ContactEndpointRoutes.ARCHIVE}"


@dataclass(frozen=True)
class EventEndpointUrls:
    BASE = f"{event_router.prefix}{EventEndpointRoutes.BASE}"
    FEED = f"{event_router.prefix}{EventEndpointRoutes.FEED}"
    MODIFY_ASSETS = f"{event_router.prefix}{EventEndpointRoutes.MODIFY_ASSETS}"


@dataclass(frozen=True)
class RecordEndpointUrls:
    BASE = f"{record_router.prefix}{RecordEndpointRoutes.BASE}"


@dataclass(frozen=True)
class LocationEndpointUrls:
    HISTORY = f"{location_router.prefix}{LocationEndpointRoutes.HISTORY}"
    PLACE = f"{location_router.prefix}{LocationEndpointRoutes.PLACE}"
    MOVEMENT = f"{location_router.prefix}{LocationEndpointRoutes.MOVEMENT}"


@dataclass(frozen=True)
class ExtensionEndpointUrls:
    LIST_RUNS = f"{extension_router.prefix}{ExtensionsEndpointRoutes.LIST_RUNS}"
    LIST_RESULTS = f"{extension_router.prefix}{ExtensionsEndpointRoutes.LIST_RESULTS}"


@dataclass(frozen=True)
class ExtensionDirectoryEndpointUrls:
    BASE = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.BASE}"
    LIST = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.LIST}"
    SUBSCRIBE = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.SUBSCRIBE}"
    UNSUBSCRIBE = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.UNSUBSCRIBE}"


@dataclass(frozen=True)
class ExtensionProviderEndpointUrls:
    BASE = f"{extension_providers_router.prefix}{ExtensionProvidersEndpointRoutes.BASE}"
    LIST = f"{extension_providers_router.prefix}{ExtensionProvidersEndpointRoutes.LIST}"


@dataclass(frozen=True)
class DataSummaryEndpointUrls:
    USER_DATA = f"{summary_router.prefix}{DataSummaryEndpointRoutes.USER_DATA}"
    TAG_COUNT = f"{summary_router.prefix}{DataSummaryEndpointRoutes.TAG_COUNT}"
    STATISTICS = f"{summary_router.prefix}{DataSummaryEndpointRoutes.STATISTICS}"
    BODY_PARTS = f"{summary_router.prefix}{DataSummaryEndpointRoutes.BODY_PARTS}"


@dataclass(frozen=True)
class AggregationEndpointUrls:
    FREQUENCY_DISTRIBUTION = f"{aggregation_router.prefix}{AggregationEndpointRoutes.FREQUENCY_DISTRIBUTION}"
    DATE_HISTOGRAM = f"{aggregation_router.prefix}{AggregationEndpointRoutes.DATE_HISTOGRAM}"
    CALENDAR_FREQUENCY_DISTRIBUTION = (
        f"{aggregation_router.prefix}{AggregationEndpointRoutes.CALENDAR_FREQUENCY_DISTRIBUTION}"
    )
    CALENDAR_HISTOGRAM_AGGREGATION = (
        f"{aggregation_router.prefix}{AggregationEndpointRoutes.CALENDAR_HISTOGRAM_AGGREGATION}"
    )


@dataclass(frozen=True)
class AnalyseEndpointUrls:
    TREND_DETECT = f"{analyse_router.prefix}{AnalyseEndpointRoutes.TREND_DETECT}"
    CORRELATE_EVENT = f"{analyse_router.prefix}{AnalyseEndpointRoutes.CORRELATE_EVENT}"
    CORRELATE_EVENT_SUGGEST_PARAMETERS = (
        f"{analyse_router.prefix}{AnalyseEndpointRoutes.CORRELATE_EVENT_SUGGEST_PARAMETERS}"
    )


@dataclass(frozen=True)
class LookupEndpointURLs:
    BASE = lookup_router.prefix + LookupEndpointRoutes.BASE


@dataclass(frozen=True)
class DocumentEndpointURLs:
    BASE = document_router.prefix + DocumentEndpointRoutes.BASE
    BY_QUERY = document_router.prefix + DocumentEndpointRoutes.BY_QUERY
    ALL_DATA = document_router.prefix + DocumentEndpointRoutes.ALL_DATA


@dataclass(frozen=True)
class DataCrudEndpointsUrls:
    BASE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BASE}"
    EVENT = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.EVENT}"
    BLOOD_PRESSURE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BLOOD_PRESSURE}"
    BLOOD_SUGAR = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BLOOD_SUGAR}"
    BODY_TEMPERATURE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BODY_TEMPERATURE}"
    HEART_RATE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.HEART_RATE}"
    NOTE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.NOTE}"
    LOCATION = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.LOCATION}"
    PULSE_OXYGEN = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.PULSE_OXYGEN}"
    RATING = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.RATING}"
    RESTING_HEART_RATE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.RESTING_HEART_RATE}"
    SLEEP = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.SLEEP}"
    STEPS = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.STEPS}"
    WEIGHT = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.WEIGHT}"


@dataclass(frozen=True)
class AIEndpointUrls:
    SUGGEST_EVENT = f"{ai_router.prefix}{AIEndpointRoutes.SUGGEST_EVENT}"
