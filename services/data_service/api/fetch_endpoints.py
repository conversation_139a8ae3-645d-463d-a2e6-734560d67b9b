from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.data_service.api.constants import DataServicePrefixes, FetchEndpointRoutes
from services.data_service.application.use_cases.by_id.fetch_by_id_use_case import FetchByIdUseCase

fetch_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.FETCH_PREFIX}",
    tags=["modify"],
    responses={404: {"description": "Not found"}},
)


@fetch_router.get(
    FetchEndpointRoutes.BY_ID,
    responses={200: {"model": dict}},
)
async def fetch_by_id_endpoint(
    data_type: DataType,
    doc_id: UUID = Query(...),
    current_uuid: UUID = Depends(get_current_uuid),
    target_use_case: FetchByIdUseCase = Injected(FetchByIdUseCase),
):
    """Gets document detail given its id and data type."""

    output_models = await target_use_case.execute_async(
        user_uuid=current_uuid, doc_ids=[doc_id], data_schema=data_type.to_domain_model()  # pyright:ignore
    )
    return [
        result.document.model_dump(by_alias=True, exclude={DocumentLabels.METADATA: {DocumentLabels.USER_UUID}})
        for result in output_models.results
    ][0]
