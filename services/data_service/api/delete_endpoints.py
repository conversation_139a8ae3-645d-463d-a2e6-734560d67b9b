from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected
from pydantic import Field

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.message_response import SingleMessageResponse
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.api.constants import DataServicePrefixes, DeleteEndpointRoutes
from services.data_service.api.response_models.delete_document_by_id_api_response import DeleteDocumentByIdAPIResponse
from services.data_service.application.enums.deletable_data_type import DeletableDataType
from services.data_service.application.use_cases.by_id.delete_by_id_use_case import (
    DeleteByIdUseCase,
)
from services.data_service.application.use_cases.delete_by_filter_use_case import DeleteByFilterUseCase

delete_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.DELETE_PREFIX}",
    tags=["delete"],
    responses={404: {"description": "Not found"}},
)


@delete_router.delete(DeleteEndpointRoutes.BY_ID, response_model=DeleteDocumentByIdAPIResponse)
async def delete_by_id_endpoint(
    data_type: DeletableDataType,
    doc_id: UUID = Query(...),
    current_uuid: UUID = Depends(get_current_uuid),
    target_use_case: DeleteByIdUseCase = Injected(DeleteByIdUseCase),
):
    """Deletes an entry that matches given doc_id"""
    result = await target_use_case.execute_async(
        user_uuid=current_uuid,
        doc_ids=[doc_id],
        data_schema=data_type.to_domain_model(),
    )
    return DeleteDocumentByIdAPIResponse.map(model=result[0])


class DeleteByFilterInputBoundary(BaseDataModel):
    data_types: List[DeletableDataType] = Field(min_length=1)
    organizations: List[Organization] = Field(min_length=1)


@delete_router.delete(DeleteEndpointRoutes.BY_FILTER, response_model=SingleMessageResponse)
async def delete_by_filter_endpoint(
    input_boundary: DeleteByFilterInputBoundary,
    current_uuid: UUID = Depends(get_current_uuid),
    target_use_case: DeleteByFilterUseCase = Injected(DeleteByFilterUseCase),
):
    """Deletes all entries matching specified data types and providers.
    Both data type and provider are applied as a filter, so specifying both
    e.k. Google and e.k. HeartRate will only remove HeartRate data synchronized from Google."""

    result = await target_use_case.execute(
        user_uuid=current_uuid,
        data_types=input_boundary.data_types,
        organizations=input_boundary.organizations,
    )
    return SingleMessageResponse(message=result)
