from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.person import PersonFields, PersonIdentifier
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdatePersonInput(UpdateEventInput, PersonIdentifier):
    type: Literal[DataType.Person] = Field(alias=EventFields.TYPE)
    category: PersonRelationship = Field(alias=PersonFields.CATEGORY)
    rating: int | None = Field(
        alias=PersonFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=EventFields.NOTE,
        min_length=1,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
            ]
        )
