import itertools
from asyncio import TaskGroup
from typing import Sequence
from uuid import UUID, uuid4

from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Service
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.document_base import EventMetadata, EventMetadataFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.metadata import MetadataFields
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.events.insert_validators import (
    ContactReferencesValidator,
    DuplicatesValidator,
    PlanReferencesValidator,
    TemplateReferencesValidator,
)
from services.data_service.application.use_cases.events.mappers.domain_event_transformer import (
    DomainEventTransformer,
)
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)


class InsertEventUseCase:
    def __init__(
        self,
        event_repo: EventRepository,
        event_mapper: DomainEventTransformer,
        template_validator: TemplateR<PERSON>ere<PERSON>Valida<PERSON>,
        plan_validator: PlanReferencesValidator,
        duplicate_validator: DuplicatesValidator,
        contact_validator: ContactReferencesValidator,
    ):
        self._event_repo = event_repo
        self._event_transformer = event_mapper
        self._template_validator = template_validator
        self._plan_validator = plan_validator
        self._duplicate_validator = duplicate_validator
        self._contact_validator = contact_validator

    async def execute_async(self, boundary: InsertEventInputBoundary, owner_id: UUID) -> Sequence[Event]:
        input_events = boundary.documents
        # validate templates
        await self._template_validator.validate(data=input_events, owner_id=owner_id)
        # validate plans
        await self._plan_validator.validate(data=input_events, owner_id=owner_id)
        # validate contacts
        await self._contact_validator.validate(data=input_events, owner_id=owner_id)
        # map to domain objets
        events = await self.map_events_and_store_assets(
            owner_id=owner_id,
            metadata=EventMetadata.map(
                model=boundary.metadata,
                fields={
                    EventMetadataFields.SERVICE: Service.DATA,
                    MetadataFields.ORGANIZATION: Organization(boundary.metadata.origin.value),
                },
            ),
            documents=input_events,
        )
        # validate duplicates
        await self._duplicate_validator.validate(documents=events)
        # store and return
        return await self._event_repo.insert(events=events)

    async def map_events_and_store_assets(
        self,
        documents: Sequence[InsertEventInputs],
        owner_id: UUID,
        metadata: EventMetadata,
        chunk_size: int = 10,  # process 10 documents at a time
    ) -> Sequence[Event]:
        events = []
        for i in range(0, len(documents), chunk_size):
            chunk = documents[i : i + chunk_size]
            async with TaskGroup() as group:
                tasks = [
                    group.create_task(
                        self._event_transformer.transform(
                            insertable_event=doc,
                            owner_id=owner_id,
                            metadata=metadata,
                            submission_id=uuid4(),
                        )
                    )
                    for doc in chunk
                ]
            events.extend(itertools.chain.from_iterable([t.result() for t in tasks]))

        return events
