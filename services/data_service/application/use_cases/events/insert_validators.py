from typing import Sequence
from uuid import UUID

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
    RuntimeException,
)
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.person import PersonFields
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs


class PlanReferencesValidator:
    def __init__(self, plan_repo: PlanRepository):
        self._plan_repo = plan_repo

    async def validate[T: InsertEventInputs](self, data: Sequence[T], owner_id: UUID):
        plan_ids = [e.plan_extension.plan_id for e in data if e.plan_extension]
        if plan_ids:
            existing_plans = await self._plan_repo.search_by_id(ids=plan_ids)
            if len(plan_ids) != len(existing_plans):
                existing_ids = [p.id for p in existing_plans]
                not_found_ids = [id for id in plan_ids if id not in existing_ids]
                raise IncorrectOperationException(message=f"could not find specified plans: {not_found_ids}")

            for plan in existing_plans:
                if plan.rbac.owner_id != owner_id:
                    raise InvalidPrivilegesException(
                        message="You don't have permission to access some of the documents"
                    )
                if plan.archived_at:
                    raise RuntimeException(message="events cannot be created from archived plans")


class TemplateReferencesValidator:
    def __init__(self, template_repo: TemplateRepository):
        self._template_repo = template_repo

    async def validate[T: InsertEventInputs](self, data: Sequence[T], owner_id: UUID):
        template_ids = [e.template_id for e in data if e.template_id]
        if template_ids:
            existing_templates = await self._template_repo.search_by_id(ids=template_ids)
            if len(template_ids) != len(existing_templates):
                existing_ids = [p.id for p in existing_templates]
                not_found_ids = [id for id in template_ids if id not in existing_ids]
                raise IncorrectOperationException(message=f"could not find specified templates: {not_found_ids}")

            for event, template in zip(
                sorted([d for d in data if d.template_id], key=lambda e: e.template_id),  # pyright: ignore
                sorted(existing_templates, key=lambda e: e.id),
            ):
                if template.rbac.owner_id != owner_id:
                    raise InvalidPrivilegesException(
                        message="You don't have permission to access some of the documents"
                    )
                if isinstance(template, EventTemplate):
                    if template.document_type != event.type_id():
                        raise IncorrectOperationException(
                            message=f"Trying to insert event of type {event.type_id()} but template of type {template.document_type}"
                        )


class DuplicatesValidator:
    def __init__(self, duplicate_check_service: DuplicateCheckService):
        self._duplicate_check_service = duplicate_check_service

    async def validate[T: Document](self, documents: Sequence[T]):
        batch_size = 100
        for i in range(0, len(documents), batch_size):
            batch = documents[i : i + batch_size]
            await self._duplicate_check_service.validate_no_document_duplicates(documents=batch)


class ContactReferencesValidator:
    def __init__(self, contact_repo: ContactRepository):
        self._contact_repo = contact_repo

    async def validate[T: InsertEventInputs](self, data: Sequence[T], owner_id: UUID):
        contact_ids = [
            e.contact_id for e in data if (hasattr(e, PersonFields.CONTACT_ID)) and e.contact_id  # pyright: ignore
        ]
        if contact_ids:
            existing_contacts = await self._contact_repo.search_by_id(ids=contact_ids)
            if len(contact_ids) != len(existing_contacts):
                existing_ids = [p.id for p in existing_contacts]
                not_found_ids = [id for id in contact_ids if id not in existing_ids]
                raise IncorrectOperationException(message=f"could not find specified contacts: {not_found_ids}")

            for contact in existing_contacts:
                if contact.rbac.owner_id != owner_id:
                    raise InvalidPrivilegesException(
                        message="You don't have permission to access some of the documents"
                    )
