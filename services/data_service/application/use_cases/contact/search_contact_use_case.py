from typing import Sequence

from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.data_service.application.use_cases.contact.models.search_contact_input_boundary import (
    SearchContactInputBoundary,
)


class SearchContactUseCase:
    def __init__(self, contact_repository: ContactRepository):
        self._contact_repository = contact_repository

    async def execute_async(self, input_boundary: SearchContactInputBoundary) -> Sequence[Contact]:
        query = CommonQueryAdjustments.add_user_uuid_to_query(
            user_uuid=input_boundary.owner_id, query=input_boundary.query.to_query()
        )
        response = await self._contact_repository.search_by_query(query=query, size=10_000)
        return response.documents
