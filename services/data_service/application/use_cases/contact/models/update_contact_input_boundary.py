from datetime import date
from typing import Optional, Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.contact import ContactFields
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.events.models.shared import IdentifiableInputDocument


class UpdateContactInput(IdentifiableInputDocument):
    last_name: NonEmptyStr = Field(alias=ContactFields.LAST_NAME, min_length=1)
    first_name: NonEmptyStr = Field(alias=ContactFields.FIRST_NAME, min_length=1)
    nickname: NonEmptyStr = Field(alias=ContactFields.NICKNAME, min_length=1)
    company: Optional[NonEmptyStr] = Field(alias=ContactFields.COMPANY, default=None)
    street: Optional[NonEmptyStr] = Field(alias=ContactFields.STREET, default=None)
    address: Optional[NonEmptyStr] = Field(alias=ContactFields.ADDRESS, default=None)
    city: Optional[NonEmptyStr] = Field(alias=ContactFields.CITY, default=None)
    state: Optional[NonEmptyStr] = Field(alias=ContactFields.STATE, default=None)
    zip: Optional[NonEmptyStr] = Field(alias=ContactFields.ZIP, default=None)
    note: Optional[NonEmptyStr] = Field(alias=ContactFields.NOTE, default=None)
    birthday: date = Field(alias=ContactFields.BIRTHDAY)
    relationship: PersonRelationship = Field(alias=ContactFields.RELATIONSHIP)
    tags: UniqueSequenceStr = Field(
        alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount, default_factory=list
    )


class UpdateContactInputBoundary(BaseDataModel):
    documents: Sequence[UpdateContactInput] = Field(min_length=1)
    owner_id: UUID
