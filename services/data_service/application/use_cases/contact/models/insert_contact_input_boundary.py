from datetime import date
from typing import Sequence
from uuid import UUID

from pydantic import Field, field_validator

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.contact import ContactFields
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.shared import BaseDataModel


class InsertContactInput(BaseDataModel):
    last_name: NonEmptyStr = Field(alias=ContactFields.LAST_NAME, min_length=1)
    first_name: NonEmptyStr = Field(alias=ContactFields.FIRST_NAME, min_length=1)
    nickname: NonEmptyStr = Field(alias=ContactFields.NICKNAME, min_length=1)
    company: NonEmptyStr | None = Field(alias=ContactFields.COMPANY)
    street: NonEmptyStr | None = Field(alias=ContactFields.STREET)
    address: NonEmptyStr | None = Field(alias=ContactFields.ADDRESS)
    city: NonEmptyStr | None = Field(alias=ContactFields.CITY)
    state: NonEmptyStr | None = Field(alias=ContactFields.STATE)
    zip: NonEmptyStr | None = Field(alias=ContactFields.ZIP)
    note: NonEmptyStr | None = Field(alias=ContactFields.NOTE)
    birthday: date = Field(alias=ContactFields.BIRTHDAY)
    relationship: PersonRelationship = Field(alias=ContactFields.RELATIONSHIP)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)


class InsertContactInputBoundary(BaseDataModel):
    documents: Sequence[InsertContactInput] = Field(min_length=1)
    owner_id: UUID

    @classmethod
    @field_validator("documents")
    def duplicates_validator(cls, documents: Sequence[InsertContactInput]) -> Sequence[InsertContactInput]:
        contact_keys = {(d.first_name, d.last_name, d.nickname) for d in documents}
        if len(contact_keys) != len(documents):
            raise ValueError("duplicate entries found")
        return documents
