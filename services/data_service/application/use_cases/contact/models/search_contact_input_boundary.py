from typing import Optional, Sequence
from uuid import UUID

from services.base.application.database.models.sorts import Sort
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.boundaries.continuation_token_input import ContinuationTokenInput


class SearchContactInputBoundary(BaseDataModel):
    owner_id: UUID
    limit: int = 1000
    sorts: Optional[Sequence[Sort]] = None
    continuation_token: Optional[ContinuationTokenInput] = None
    query: SingleDocumentTypeQuery
