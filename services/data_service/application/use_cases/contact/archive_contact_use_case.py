from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import IncorrectOperationException
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.schemas.contact import Contact


class ArchiveContactUseCase:
    def __init__(self, contact_repository: ContactRepository):
        self._contact_repository = contact_repository

    async def execute_async(self, owner_id: UUID, contact_ids: Sequence[UUID]) -> Sequence[Contact]:
        # Get existing contacts
        existing_contacts = await self._contact_repository.search_by_id(ids=contact_ids)

        if len(existing_contacts) != len(contact_ids):
            raise IncorrectOperationException("Some contacts were not found")

        # Verify ownership
        for contact in existing_contacts:
            if contact.rbac.owner_id != owner_id:
                raise IncorrectOperationException("Contact not owned by user")

        # Archive contacts by setting archived_at timestamp
        archived_contacts = []
        archive_timestamp = datetime.now(timezone.utc)

        for contact in existing_contacts:
            archived_contact = Contact(
                id=contact.id,
                type=contact.type,
                last_name=contact.last_name,
                first_name=contact.first_name,
                nickname=contact.nickname,
                company=contact.company,
                street=contact.street,
                address=contact.address,
                city=contact.city,
                state=contact.state,
                zip=contact.zip,
                note=contact.note,
                birthday=contact.birthday,
                relationship=contact.relationship,
                rbac=contact.rbac,
                tags=contact.tags,
                system_properties=contact.system_properties,
                archived_at=archive_timestamp,
            )
            archived_contacts.append(archived_contact)

        return await self._contact_repository.update(contacts=archived_contacts)
