from typing import Sequence
from uuid import uuid4

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.events.document_base import RBACSchema
from services.data_service.application.use_cases.contact.models.insert_contact_input_boundary import (
    InsertContactInputBoundary,
)


class InsertContactUseCase:
    def __init__(
        self,
        contact_repository: ContactRepository,
        duplicate_check_service: DuplicateCheckService,
    ):
        self._contact_repository = contact_repository
        self._duplicate_check_service = duplicate_check_service

    async def execute_async(self, input_boundary: InsertContactInputBoundary) -> Sequence[Contact]:
        contacts = [
            Contact(
                id=uuid4(),
                type=DataType.Contact,
                last_name=contact_input.last_name,
                first_name=contact_input.first_name,
                nickname=contact_input.nickname,
                company=contact_input.company,
                street=contact_input.street,
                address=contact_input.address,
                city=contact_input.city,
                state=contact_input.state,
                zip=contact_input.zip,
                note=contact_input.note,
                birthday=contact_input.birthday,
                relationship=contact_input.relationship,
                tags=contact_input.tags,
                rbac=RBACSchema(owner_id=input_boundary.owner_id),
                archived_at=None,
            )
            for contact_input in input_boundary.documents
        ]

        # Check for duplicates in the database
        await self._duplicate_check_service.validate_no_document_duplicates(documents=contacts)

        return await self._contact_repository.insert(contacts=contacts, force_strong_consistency=True)
