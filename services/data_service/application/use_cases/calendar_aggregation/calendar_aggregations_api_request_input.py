from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends
from pydantic import Field

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.schemas.query.aggregations import SimpleAggregationMethod
from services.data_service.api.queries.event_query_api import EventQueryAPI
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregations_input_boundary import (
    CalendarFrequencyDistributionInputBoundary,
    CalendarHistogramAggregationInputBoundary,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_frequency_distribution_use_case import (
    CalendarAggregationType,
)


class CalendarFrequencyDistributionAPIRequestInput(EventQueryAPI):
    calendar_aggregation_type: CalendarAggregationType = Field()
    fill_null_values: bool = Field(default=True)

    @staticmethod
    def to_input_boundary(
        request_input: CalendarFrequencyDistributionAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> CalendarFrequencyDistributionInputBoundary:
        return CalendarFrequencyDistributionInputBoundary(
            calendar_aggregation_type=request_input.calendar_aggregation_type,
            fill_null_values=request_input.fill_null_values,
            query=request_input.to_query(),
            owner_id=owner_id,
        )


class CalendarHistogramAggregationAPIRequestInput(EventQueryAPI):
    calendar_aggregation_type: CalendarAggregationType = Field()
    fill_null_values: bool = Field(default=True)
    field_name: str = Field()
    aggregation_method: SimpleAggregationMethod = Field()

    @staticmethod
    def to_input_boundary(
        request_input: CalendarHistogramAggregationAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> CalendarHistogramAggregationInputBoundary:
        return CalendarHistogramAggregationInputBoundary(
            calendar_aggregation_type=request_input.calendar_aggregation_type,
            fill_null_values=request_input.fill_null_values,
            field_name=request_input.field_name,
            aggregation_method=request_input.aggregation_method,
            query=request_input.to_query(),
            owner_id=owner_id,
        )
