import itertools
from asyncio import TaskGroup
from datetime import datetime, timedelta
from typing import Literal, Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import InsertableOrigin, SourceOS, SourceService
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.rrule import get_timedelta_from_rrule
from services.base.domain.schemas.events.event import Event, EventPlanExtension
from services.base.domain.schemas.events.plan import Plan, PlanStreak
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template
from services.base.domain.schemas.templates.template_payloads import TemplatePayloads
from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.shared import EventMetadataInput
from services.data_service.application.use_cases.plans.models.complete_plans_input_boundary import (
    CompletePlanInput,
    CompletePlansInputBoundary,
)
from services.data_service.application.use_cases.plans.models.complete_plans_output_boundary import (
    CompletePlansOutputBoundary,
    CompletePlansOutputBoundaryItem,
)
from services.data_service.application.use_cases.templates.template_validator import TemplateValidator
from services.data_service.type_resolver import TypeResolver


class CompletePlansUseCase:
    def __init__(
        self,
        plan_repo: PlanRepository,
        template_repo: TemplateRepository,
        insert_event_uc: InsertEventUseCase,
        template_validator: TemplateValidator,
    ):
        self._plan_repo = plan_repo
        self._template_repo = template_repo
        self._insert_event_uc = insert_event_uc
        self._template_validator = template_validator

    async def execute_async(
        self,
        input_boundary: CompletePlansInputBoundary,
    ) -> CompletePlansOutputBoundary:
        input_plans = input_boundary.documents
        existing_plans = await self._fetch_plans_from_db(complete_inputs=input_plans, owner_id=input_boundary.owner_id)

        completed_plans = []
        event_inputs = []

        complete_input: CompletePlanInput
        plan: Plan
        for complete_input, plan in zip(
            sorted(input_plans, key=lambda p: p.id), sorted(existing_plans, key=lambda p: p.id)
        ):
            updated_plan = self._update_plan(complete_input=complete_input, existing_plan=plan)
            completed_plans.append(updated_plan)

            template = (
                await self._template_validator.fetch_and_validate_templates(
                    template_ids=[plan.template_id], owner_id=input_boundary.owner_id
                )
            )[0]
            returned_event_inputs = await self._collect_insert_event_inputs(
                template=template, complete_input=complete_input, owner_id=input_boundary.owner_id
            )
            for event_input in returned_event_inputs:
                event_inputs.append(event_input)

        async with TaskGroup() as group:
            tasks = [
                group.create_task(self._insert_event_uc.execute_async(boundary=b, owner_id=input_boundary.owner_id))
                for b in event_inputs
            ]
        events: Sequence[Event] = list(itertools.chain(*[t.result() for t in tasks]))
        updated_plans = await self._plan_repo.update(plans=completed_plans)
        out = []
        for p in updated_plans:
            docs = [e for e in events if e.plan_extension and (e.plan_extension.plan_id == p.id)]
            out.append(CompletePlansOutputBoundaryItem(events=docs, plan=p))
        return CompletePlansOutputBoundary(documents=out)

    async def _fetch_plans_from_db(
        self, complete_inputs: Sequence[CompletePlanInput], owner_id: UUID
    ) -> Sequence[Plan]:
        existing_plans: Sequence[Plan] = await self._plan_repo.search_by_id(ids=[d.id for d in complete_inputs])

        if len(complete_inputs) != len(existing_plans):
            existing_ids = [e.id for e in existing_plans]
            not_found_ids = [str(p.id) for p in complete_inputs if p.id not in existing_ids]
            raise IncorrectOperationException(message=f"Plans {not_found_ids} were not found")

        for plan in existing_plans:
            if plan.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to access some of the documents")
            if plan.archived_at:
                raise IncorrectOperationException(message="completing archived plan")
        return existing_plans

    @staticmethod
    def _is_plan(
        completed_at: datetime,
        next_scheduled_at: datetime,
        recurrence: CustomRRule,
    ) -> Literal["too_early", "too_late", "on_time"]:
        # Validate previous tick, or calculate it if not available
        previous_tick = recurrence.before(dt=next_scheduled_at)
        if not previous_tick:
            period = get_timedelta_from_rrule(rule=recurrence)
            previous_tick = next_scheduled_at - period

        if completed_at < previous_tick:
            return "too_early"
        elif completed_at >= recurrence.after(dt=next_scheduled_at):
            return "too_late"
        else:
            return "on_time"

    @staticmethod
    def _calculate_streak_counters(
        streak: PlanStreak, plan_is: Literal["too_early", "too_late", "on_time"]
    ) -> PlanStreak:
        new_total_triggered = streak.total_triggered + 1
        new_streak = streak.streak

        # Completed too late: reset streak
        if plan_is == "too_early":
            pass
        # Completed too early: streak does not change
        elif plan_is == "too_late":
            new_streak = 0
        # Default -> increment streak
        else:
            new_streak += 1

        new_longest_streak = max(streak.longest_streak, new_streak)
        return PlanStreak(streak=new_streak, longest_streak=new_longest_streak, total_triggered=new_total_triggered)

    @staticmethod
    def _calculate_next_schedule_and_recurrence(
        completed_at: datetime,
        next_scheduled_at: datetime,
        recurrence: CustomRRule,
        is_plan_schedule_absolute: bool,
        plan_is: Literal["too_early", "too_late", "on_time"],
    ) -> tuple[datetime | None, CustomRRule]:
        if is_plan_schedule_absolute:
            # Completed too early -> just keep current schedule
            if plan_is == "too_early":
                return next_scheduled_at, recurrence
            # Completed too late -> skip ticks of the plans to current time
            elif plan_is == "too_late":
                return recurrence.after(dt=completed_at), recurrence
            # Default -> schedule next tick
            else:
                return recurrence.after(dt=next_scheduled_at), recurrence
        else:
            rec = recurrence.replace(dtstart=completed_at)
            next_schedule_from = completed_at
            return rec.after(dt=next_schedule_from), rec

    @staticmethod
    def _should_archive(max_completed: int | None, current_completed: int) -> bool:
        if not max_completed:
            return False
        return True if (current_completed >= max_completed) else False

    @classmethod
    def _get_updated_plan_fields(
        cls, complete_input: CompletePlanInput, existing_plan: Plan
    ) -> tuple[datetime, CustomRRule, datetime | None, int, PlanStreak]:
        if not existing_plan.recurrence:
            raise ShouldNotReachHereException(f"plan {existing_plan.id} recurrence not set")
        current_completed = existing_plan.current_completed + 1
        plan_is = cls._is_plan(
            completed_at=complete_input.completed_at,
            next_scheduled_at=existing_plan.next_scheduled_at,
            recurrence=existing_plan.recurrence,
        )
        next_scheduled_at, recurrence = CompletePlansUseCase._calculate_next_schedule_and_recurrence(
            completed_at=complete_input.completed_at,
            is_plan_schedule_absolute=existing_plan.is_absolute_schedule,
            next_scheduled_at=existing_plan.next_scheduled_at,
            recurrence=existing_plan.recurrence,
            plan_is=plan_is,
        )

        archive = not next_scheduled_at or CompletePlansUseCase._should_archive(
            max_completed=existing_plan.max_completed,
            current_completed=current_completed,
        )

        archived_at = complete_input.completed_at if archive else None

        if not next_scheduled_at:
            if not archived_at:
                raise ShouldNotReachHereException(
                    f"plan {existing_plan.id} at the end of recurrence {existing_plan.recurrence}"
                )
            # If archived at, we can just keep current next schedule
            next_scheduled_at = existing_plan.next_scheduled_at

        updated_streak = CompletePlansUseCase._calculate_streak_counters(streak=existing_plan.streak, plan_is=plan_is)

        return next_scheduled_at, recurrence, archived_at, current_completed, updated_streak

    @classmethod
    def _update_plan(cls, complete_input: CompletePlanInput, existing_plan: Plan):
        if existing_plan.recurrence:
            next_scheduled_at, recurrence, archived_at, current_completed, updated_streak = (
                cls._get_updated_plan_fields(existing_plan=existing_plan, complete_input=complete_input)
            )
        else:
            next_scheduled_at = existing_plan.next_scheduled_at
            recurrence = None
            archived_at = complete_input.completed_at
            current_completed = existing_plan.current_completed + 1
            updated_streak = existing_plan.streak

        return Plan(
            # calculated
            archived_at=archived_at,
            current_completed=current_completed,
            next_scheduled_at=next_scheduled_at,
            streak=updated_streak,
            recurrence=recurrence,
            # from input
            type=DataType.Plan,
            id=existing_plan.id,
            rbac=existing_plan.rbac,
            metadata=existing_plan.metadata,
            name=existing_plan.name,
            template_id=existing_plan.template_id,
            is_urgent=existing_plan.is_urgent,
            is_confirmation_required=existing_plan.is_confirmation_required,
            note=existing_plan.note,
            is_absolute_schedule=existing_plan.is_absolute_schedule,
            priority=existing_plan.priority,
            prompt=existing_plan.prompt,
            first_completed_at=existing_plan.first_completed_at or complete_input.completed_at,
            max_completed=existing_plan.max_completed,
            tags=existing_plan.tags,
        )

    async def _collect_insert_event_inputs(
        self, template: Template, complete_input: CompletePlanInput, owner_id: UUID
    ) -> Sequence[InsertEventInputBoundary]:
        if isinstance(template, EventTemplate):
            payload = template.document
            if complete_input.payload:
                if not isinstance(complete_input.payload, type(template.document)):
                    raise IncorrectOperationException(
                        f"failed to complete plan {complete_input.id} - input payload mismatches template payload type {template.document_type.value}"
                    )
                payload = complete_input.payload
            return self._collect_from_event_template_payload(
                payload=payload,
                complete_input=complete_input,
                template_id=template.id,
            )
        elif isinstance(template, GroupTemplate):
            return await self._collect_from_group_template(
                template=template, owner_id=owner_id, complete_input=complete_input
            )

        else:
            raise ShouldNotReachHereException(f"unexpected template input {type(template)}")

    @staticmethod
    def _collect_from_event_template_payload(
        payload: TemplatePayloads, template_id: UUID, complete_input: CompletePlanInput
    ) -> Sequence[InsertEventInputBoundary]:
        timestamp = complete_input.completed_at
        end_time = (timestamp + timedelta(seconds=payload.duration)) if payload.duration else None
        insertable_model = TypeResolver.get_insert_event_input(type_id=payload.type_id())

        event_input = insertable_model(
            **payload.model_dump(by_alias=True),
            timestamp=timestamp,
            end_time=end_time,
            template_id=template_id,
            plan_extension=EventPlanExtension(plan_id=complete_input.id),
        )
        return [
            InsertEventInputBoundary(
                documents=[event_input],
                metadata=EventMetadataInput(
                    origin=InsertableOrigin.LLIF,
                    origin_device=None,
                    source_os=SourceOS.UNKNOWN,
                    source_service=SourceService.BEST_LIFE_APP,
                ),
            )
        ]

    async def _collect_from_group_template(
        self, template: GroupTemplate, complete_input: CompletePlanInput, owner_id: UUID
    ) -> Sequence[InsertEventInputBoundary]:
        templates = await self._template_validator.fetch_and_validate_templates(
            template_ids=template.template_ids, owner_id=owner_id
        )
        if not templates:
            raise ShouldNotReachHereException(f"No associated templates for group template with id:{template.id}")
        event_inputs = []
        for child_template in templates:
            event_input = await self._collect_insert_event_inputs(
                template=child_template, complete_input=complete_input, owner_id=owner_id
            )
            event_inputs.extend(event_input)
        return event_inputs
