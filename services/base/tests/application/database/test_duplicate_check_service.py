import pytest

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.exceptions import DuplicateDocumentsFound
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.query.query import Query
from services.base.tests.domain.builders.contact_builder import ContactBuilder
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.type_resolver import TypeResolver


class TestDuplicateCheckService:
    async def test_build_validation_query_n(
        self,
        duplicate_check_service: DuplicateCheckService,
    ):
        events = EventBuilder().build_n(n=100)
        query: Query = duplicate_check_service._build_validation_query(documents=events)
        unique_types = list({type(e) for e in events})

        assert len(query.type_queries) == len(unique_types)

        for type_query in query.type_queries:
            assert all([tp in unique_types for tp in type_query.domain_types])

    @pytest.mark.parametrize("builder", [b for b in TypeResolver.EVENT_BUILDERS])
    async def test_validate_no_document_duplicates_raises_error(
        self,
        builder,
        duplicate_check_service: DuplicateCheckService,
        event_repo: EventRepository,
    ):
        events = builder().build_n()
        await event_repo.insert(events=events, force_strong_consistency=True)
        with pytest.raises(DuplicateDocumentsFound):
            await duplicate_check_service.validate_no_document_duplicates(documents=events)
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in events])

    async def test_validate_no_document_duplicates_none_found(
        self,
        duplicate_check_service: DuplicateCheckService,
    ):
        events = EventBuilder().build_all()
        assert await duplicate_check_service.validate_no_document_duplicates(documents=events) is None

    async def test_validate_contact_duplicates_raises_error(
        self,
        duplicate_check_service: DuplicateCheckService,
        contact_repo: ContactRepository,
    ):
        # Create a contact with specific first_name, last_name, nickname
        contact = ContactBuilder().with_first_name("John").with_last_name("Doe").with_nickname("JD").build()

        # Insert the contact into the database
        await contact_repo.insert(contacts=[contact], force_strong_consistency=True)

        # Try to validate a duplicate contact (same first_name, last_name, nickname, owner_id)
        duplicate_contact = (
            ContactBuilder()
            .with_first_name("John")
            .with_last_name("Doe")
            .with_nickname("JD")
            .with_owner_id(contact.rbac.owner_id)
            .build()
        )

        # Should raise DuplicateDocumentsFound
        with pytest.raises(DuplicateDocumentsFound):
            await duplicate_check_service.validate_no_document_duplicates(documents=[duplicate_contact])

        # Cleanup
        await contact_repo.delete_by_id(ids=[contact.id])

    async def test_validate_contact_duplicates_none_found(
        self,
        duplicate_check_service: DuplicateCheckService,
    ):
        # Create contacts with different combinations of first_name, last_name, nickname
        contacts = [
            ContactBuilder().with_first_name("John").with_last_name("Doe").with_nickname("JD").build(),
            ContactBuilder().with_first_name("Jane").with_last_name("Doe").with_nickname("JD").build(),
            ContactBuilder().with_first_name("John").with_last_name("Smith").with_nickname("JD").build(),
            ContactBuilder().with_first_name("John").with_last_name("Doe").with_nickname("Johnny").build(),
        ]

        # Should not raise any exception since all contacts have different combinations
        assert await duplicate_check_service.validate_no_document_duplicates(documents=contacts) is None
