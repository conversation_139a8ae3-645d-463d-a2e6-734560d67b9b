from typing import Sequence
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.body_metric.blood_glucose import (
    BloodGlucose,
    BloodGlucoseIdentifier,
    BloodGlucoseSpecimenSource,
    BloodGlucoseValueLimits,
)
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class BloodGlucoseBuilder(EventBuilderBase, BloodGlucoseIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> BloodGlucose:
        return BloodGlucose(
            type=DataType.BloodGlucose,
            template_id=None,
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            submission_id=self._submission_id or uuid4(),
            value=PrimitiveTypesGenerator.generate_random_float(
                min_value=BloodGlucoseValueLimits.MINIMUM_VALUE, max_value=BloodGlucoseValueLimits.MAXIMUM_VALUE
            ),
            specimen_source=PrimitiveTypesGenerator.generate_random_enum(enum_type=BloodGlucoseSpecimenSource),
            note=PrimitiveTypesGenerator.generate_random_string(max_length=EventValueLimits.MAX_NOTE_LENGTH),
            id=uuid4(),
            group_id=self._group_id,
            asset_references=self._asset_references,
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[BloodGlucose]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
