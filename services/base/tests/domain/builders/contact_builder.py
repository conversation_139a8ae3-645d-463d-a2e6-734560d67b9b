from datetime import date, datetime
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.events.document_base import RBACSchema


class ContactBuilder:
    def __init__(self):
        self._id: UUID | None = None
        self._last_name: str | None = None
        self._first_name: str | None = None
        self._nickname: str | None = None
        self._company: str | None = None
        self._street: str | None = None
        self._address: str | None = None
        self._city: str | None = None
        self._state: str | None = None
        self._zip: str | None = None
        self._note: str | None = None
        self._birthday: date | None = None
        self._relationship: PersonRelationship | None = None
        self._owner_id: UUID | None = None
        self._tags: list[str] | None = None
        self._archived_at: datetime | None = None

    def build(self) -> Contact:
        return Contact(
            id=self._id or uuid4(),
            type=DataType.Contact,
            last_name=self._last_name or PrimitiveTypesGenerator.generate_random_string(max_length=50),
            first_name=self._first_name or PrimitiveTypesGenerator.generate_random_string(max_length=50),
            nickname=self._nickname or PrimitiveTypesGenerator.generate_random_string(max_length=50),
            company=self._company or PrimitiveTypesGenerator.generate_random_string(max_length=100, allow_none=True),
            street=self._street or PrimitiveTypesGenerator.generate_random_string(max_length=100, allow_none=True),
            address=self._address or PrimitiveTypesGenerator.generate_random_string(max_length=100, allow_none=True),
            city=self._city or PrimitiveTypesGenerator.generate_random_string(max_length=50, allow_none=True),
            state=self._state or PrimitiveTypesGenerator.generate_random_string(max_length=50, allow_none=True),
            zip=self._zip or PrimitiveTypesGenerator.generate_random_string(max_length=20, allow_none=True),
            note=self._note or PrimitiveTypesGenerator.generate_random_string(max_length=500, allow_none=True),
            birthday=self._birthday or PrimitiveTypesGenerator.generate_random_date(),
            relationship=self._relationship
            or PrimitiveTypesGenerator.generate_random_enum(enum_type=PersonRelationship),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            archived_at=self._archived_at,
        )

    def build_n(self, n: int | None = None) -> Sequence[Contact]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_id(self, contact_id: UUID) -> Self:
        self._id = contact_id
        return self

    def with_last_name(self, last_name: str) -> Self:
        self._last_name = last_name
        return self

    def with_first_name(self, first_name: str) -> Self:
        self._first_name = first_name
        return self

    def with_nickname(self, nickname: str) -> Self:
        self._nickname = nickname
        return self

    def with_company(self, company: str) -> Self:
        self._company = company
        return self

    def with_street(self, street: str) -> Self:
        self._street = street
        return self

    def with_address(self, address: str) -> Self:
        self._address = address
        return self

    def with_city(self, city: str) -> Self:
        self._city = city
        return self

    def with_state(self, state: str) -> Self:
        self._state = state
        return self

    def with_zip(self, zip_code: str) -> Self:
        self._zip = zip_code
        return self

    def with_note(self, note: str) -> Self:
        self._note = note
        return self

    def with_birthday(self, birthday: date) -> Self:
        self._birthday = birthday
        return self

    def with_relationship(self, relationship: PersonRelationship) -> Self:
        self._relationship = relationship
        return self

    def with_owner_id(self, owner_id: UUID) -> Self:
        self._owner_id = owner_id
        return self

    def with_tags(self, tags: list[str]) -> Self:
        self._tags = tags
        return self

    def with_archived_at(self, archived_at: datetime) -> Self:
        self._archived_at = archived_at
        return self
