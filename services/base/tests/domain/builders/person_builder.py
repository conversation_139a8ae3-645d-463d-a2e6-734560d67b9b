from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.person import Person, PersonIdentifier
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class PersonBuilder(EventBuilderBase, PersonIdentifier):
    def __init__(self):
        super().__init__()
        self._contact_id: UUID | None = None
        self._rating: int | None = None
        self._note: str | None = None

    def build(self) -> Person:
        return Person(
            type=DataType.Person,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=PersonRelationship),
            template_id=None,
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            submission_id=self._submission_id or uuid4(),
            rating=self._rating or PrimitiveTypesGenerator.generate_random_int(max_value=10, min_value=0),
            id=uuid4(),
            group_id=self._group_id,
            asset_references=self._asset_references,
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            note=self._note or PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            plan_extension=None,
            contact_id=self._contact_id or uuid4(),
        )

    def build_n(self, n: int | None = None) -> Sequence[Person]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_contact_id(self, contact_id: UUID) -> Self:
        self._contact_id = contact_id
        return self

    def with_rating(self, rating: int) -> Self:
        self._rating = rating
        return self

    def with_note(self, note: str) -> Self:
        self._note = note
        return self
