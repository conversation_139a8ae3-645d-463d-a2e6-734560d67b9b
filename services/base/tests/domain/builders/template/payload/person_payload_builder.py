from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Insertable<PERSON>rigin, Origin
from services.base.domain.schemas.events.person import PersonIdentifier
from services.base.domain.schemas.templates.payload.person_template_payload import PersonTemplatePayload
from services.base.tests.domain.builders.person_builder import PersonBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class PersonPayloadBuilder(EventPayloadBuilderBase, PersonIdentifier):

    def build(self) -> PersonTemplatePayload:
        return PersonTemplatePayload.map(
            model=PersonBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
