from typing import Dict

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.air_quality import AirQuality
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucose
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressure
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetric
from services.base.domain.schemas.events.content.audio import Audio
from services.base.domain.schemas.events.content.content import Content
from services.base.domain.schemas.events.content.image import Image
from services.base.domain.schemas.events.content.interactive import Interactive
from services.base.domain.schemas.events.content.text import Text
from services.base.domain.schemas.events.content.video import Video
from services.base.domain.schemas.events.core_event import CoreEvent
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.events.exercise.cardio import Cardio
from services.base.domain.schemas.events.exercise.exercise import Exercise
from services.base.domain.schemas.events.exercise.strength import Strength
from services.base.domain.schemas.events.feeling.emotion import Emotion
from services.base.domain.schemas.events.feeling.stress import Stress
from services.base.domain.schemas.events.medication.medication import Medication
from services.base.domain.schemas.events.note import Note
from services.base.domain.schemas.events.nutrition.drink import Drink
from services.base.domain.schemas.events.nutrition.food import Food
from services.base.domain.schemas.events.nutrition.supplement import Supplement
from services.base.domain.schemas.events.person import Person
from services.base.domain.schemas.events.plan import Plan
from services.base.domain.schemas.events.sleep_v3 import SleepV3
from services.base.domain.schemas.events.symptom import Symptom
from services.base.domain.schemas.events.use_case import UseCase
from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.pollen import Pollen
from services.base.domain.schemas.records.sleep_record import SleepRecord
from services.base.domain.schemas.resting_heart_rate import RestingHeartRate
from services.base.domain.schemas.shopping_activity import ShoppingActivity
from services.base.domain.schemas.sleep import Sleep
from services.base.domain.schemas.steps import Steps
from services.base.domain.schemas.temp_plan import TempPlan
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.usage_statistics_output import UsageStatistics
from services.base.domain.schemas.user_action_log import UserActionLog
from services.base.domain.schemas.weather import Weather
from services.base.infrastructure.database.opensearch.index_settings.air_quality import AirQualityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.body_metric import BodyMetricIndexModel
from services.base.infrastructure.database.opensearch.index_settings.contact import ContactIndexModel
from services.base.infrastructure.database.opensearch.index_settings.content import ContentIndexModel
from services.base.infrastructure.database.opensearch.index_settings.core_event import CoreEventIndexModel
from services.base.infrastructure.database.opensearch.index_settings.diary_events import DiaryEventsIndexModel
from services.base.infrastructure.database.opensearch.index_settings.event_group import EventGroupIndexModel
from services.base.infrastructure.database.opensearch.index_settings.exercise import ExerciseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.extension import (
    ExtensionIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.feeling import FeelingIndexModel
from services.base.infrastructure.database.opensearch.index_settings.heart_rate import HeartRateIndexModel
from services.base.infrastructure.database.opensearch.index_settings.inbox_message_index import InboxMessageIndexModel
from services.base.infrastructure.database.opensearch.index_settings.location import LocationIndexModel
from services.base.infrastructure.database.opensearch.index_settings.medication import MedicationIndexModel
from services.base.infrastructure.database.opensearch.index_settings.note import NoteIndexModel
from services.base.infrastructure.database.opensearch.index_settings.nutrition import NutritionIndexModel
from services.base.infrastructure.database.opensearch.index_settings.person import PersonIndexModel
from services.base.infrastructure.database.opensearch.index_settings.plan import PlanIndexModel
from services.base.infrastructure.database.opensearch.index_settings.pollen import PollenIndexModel
from services.base.infrastructure.database.opensearch.index_settings.records.sleep_record import SleepRecordIndexModel
from services.base.infrastructure.database.opensearch.index_settings.resting_heart_rate import (
    RestingHeartRateIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.shopping_activity import ShoppingActivityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.sleep import SleepIndexModel
from services.base.infrastructure.database.opensearch.index_settings.sleep_v3 import SleepV3IndexModel
from services.base.infrastructure.database.opensearch.index_settings.steps import StepsIndexModel
from services.base.infrastructure.database.opensearch.index_settings.symptom import SymptomIndexModel
from services.base.infrastructure.database.opensearch.index_settings.temp_plan import TempPlanIndexModel
from services.base.infrastructure.database.opensearch.index_settings.template import TemplateIndexModel
from services.base.infrastructure.database.opensearch.index_settings.usage_statistics_output import (
    UsageStatisticsIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.use_case import UseCaseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.user_action_log import UserLogsIndexModel
from services.base.infrastructure.database.opensearch.index_settings.weather import WeatherIndexModel
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    AIR_QUALITY_INDEX,
    DIARY_EVENTS_INDEX,
    HEART_RATE_INDEX,
    INBOX_MESSAGE_INDEX,
    LOCATION_HISTORY_INDEX,
    POLLEN_INDEX,
    RESTING_HEART_RATE_INDEX,
    SHOPPING_ACTIVITY_INDEX,
    SLEEP_INDEX,
    STEPS_INDEX,
    TEMP_PLAN_INDEX,
    USER_LOGS_INDEX,
    WEATHER_INDEX,
    OpenSearchIndex,
)

DataTypeToIndexModelMapping: Dict[DataType, OpenSearchIndex] = {
    # Content
    DataType.Audio: ContentIndexModel,
    DataType.Content: ContentIndexModel,
    DataType.Interactive: ContentIndexModel,
    DataType.Image: ContentIndexModel,
    DataType.Text: ContentIndexModel,
    DataType.Video: ContentIndexModel,
    # Exercise
    DataType.Cardio: ExerciseIndexModel,
    DataType.Exercise: ExerciseIndexModel,
    DataType.Strength: ExerciseIndexModel,
    # Body Metrics
    DataType.BloodGlucose: BodyMetricIndexModel,
    DataType.BloodPressure: BodyMetricIndexModel,
    DataType.BodyMetric: BodyMetricIndexModel,
    # Feelings
    DataType.Emotion: FeelingIndexModel,
    DataType.Stress: FeelingIndexModel,
    # Nutrition
    DataType.Drink: NutritionIndexModel,
    DataType.Food: NutritionIndexModel,
    DataType.Supplement: NutritionIndexModel,
    # Core
    DataType.CoreEvent: CoreEventIndexModel,
    DataType.Note: NoteIndexModel,
    DataType.Symptom: SymptomIndexModel,
    DataType.Person: PersonIndexModel,
    DataType.EventGroup: EventGroupIndexModel,
    DataType.SleepV3: EventGroupIndexModel,
    # Records
    DataType.SleepRecord: SleepRecordIndexModel,
    # Documents
    DataType.EventTemplate: TemplateIndexModel,
    DataType.GroupTemplate: TemplateIndexModel,
    DataType.Plan: PlanIndexModel,
    DataType.UseCase: UseCaseIndexModel,
    DataType.Contact: ContactIndexModel,
    # Events
    DataType.DiaryEvents: DiaryEventsIndexModel,
    DataType.HeartRate: HeartRateIndexModel,
    DataType.Location: LocationIndexModel,
    DataType.RestingHeartRate: RestingHeartRateIndexModel,
    DataType.ShoppingActivity: ShoppingActivityIndexModel,
    DataType.Sleep: SleepIndexModel,
    DataType.Steps: StepsIndexModel,
    # Environment
    DataType.AirQuality: AirQualityIndexModel,
    DataType.Weather: WeatherIndexModel,
    DataType.Pollen: PollenIndexModel,
    # Other
    DataType.InboxMessage: InboxMessageIndexModel,
    DataType.ExtensionRun: ExtensionIndexModel,
    DataType.ExtensionResult: ExtensionIndexModel,
    DataType.TempPlan: TempPlanIndexModel,
    DataType.UserLogs: UserLogsIndexModel,
    DataType.UsageStatistics: UsageStatisticsIndexModel,
}

DataSchemaToIndexModelMapping: Dict[type[Document], OpenSearchIndex] = {
    # Content
    Audio: ContentIndexModel,
    Content: ContentIndexModel,
    Interactive: ContentIndexModel,
    Image: ContentIndexModel,
    Text: ContentIndexModel,
    Video: ContentIndexModel,
    # Exercise
    Cardio: ExerciseIndexModel,
    Exercise: ExerciseIndexModel,
    Strength: ExerciseIndexModel,
    # Body Metrics
    BloodPressure: BodyMetricIndexModel,
    BloodGlucose: BodyMetricIndexModel,
    BodyMetric: BodyMetricIndexModel,
    # Feelings
    Emotion: FeelingIndexModel,
    Stress: FeelingIndexModel,
    # Nutrition
    Drink: NutritionIndexModel,
    Food: NutritionIndexModel,
    Supplement: NutritionIndexModel,
    # Core
    CoreEvent: CoreEventIndexModel,
    SleepV3: SleepV3IndexModel,
    EventGroup: EventGroupIndexModel,
    Note: NoteIndexModel,
    Symptom: SymptomIndexModel,
    Medication: MedicationIndexModel,
    Person: PersonIndexModel,
    # Documents
    EventTemplate: TemplateIndexModel,
    GroupTemplate: TemplateIndexModel,
    Plan: PlanIndexModel,
    UseCase: UseCaseIndexModel,
    Contact: ContactIndexModel,
    # Events
    DiaryEvents: DiaryEventsIndexModel,
    HeartRate: HeartRateIndexModel,
    Location: LocationIndexModel,
    RestingHeartRate: RestingHeartRateIndexModel,
    ShoppingActivity: ShoppingActivityIndexModel,
    Sleep: SleepIndexModel,
    Steps: StepsIndexModel,
    # Records
    SleepRecord: SleepRecordIndexModel,
    # Environment
    AirQuality: AirQualityIndexModel,
    Pollen: PollenIndexModel,
    Weather: WeatherIndexModel,
    # Other
    ExtensionRun: ExtensionIndexModel,
    ExtensionResult: ExtensionIndexModel,
    InboxMessage: InboxMessageIndexModel,
    TempPlan: TempPlanIndexModel,
    UserActionLog: UserLogsIndexModel,
    UsageStatistics: UsageStatisticsIndexModel,
}

# TODO: Should be eventually removed entirely and replaced by type field
OpenSearchIndexToDocumentModelMapping: Dict[str, type[Document]] = {
    # EventsV2
    DIARY_EVENTS_INDEX: DiaryEvents,
    LOCATION_HISTORY_INDEX: Location,
    HEART_RATE_INDEX: HeartRate,
    RESTING_HEART_RATE_INDEX: RestingHeartRate,
    SHOPPING_ACTIVITY_INDEX: ShoppingActivity,
    SLEEP_INDEX: Sleep,
    STEPS_INDEX: Steps,
    # Environment
    AIR_QUALITY_INDEX: AirQuality,
    WEATHER_INDEX: Weather,
    POLLEN_INDEX: Pollen,
    # Other
    # ExtensionIndexModel: ExtensionRun,
    # ExtensionIndexModel: ExtensionResult,
    INBOX_MESSAGE_INDEX: InboxMessage,
    TEMP_PLAN_INDEX: TempPlan,
    USER_LOGS_INDEX: UserActionLog,
}


def data_type_to_index_mapping_get(data_type: DataType) -> str:
    return DataTypeToIndexModelMapping[data_type].name
