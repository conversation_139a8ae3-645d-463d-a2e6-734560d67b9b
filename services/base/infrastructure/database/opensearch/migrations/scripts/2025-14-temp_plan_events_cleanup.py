import asyncio
import logging
from typing import Sequence

from opensearchpy import Async<PERSON><PERSON>Search

from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataTypeToIndexModelMapping
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async

logger = logging.getLogger()
logger.setLevel(logging.INFO)


class MigrateVoiceEntries:
    async def migrate(self, client: AsyncOpenSearch, data_type: DataType, dry_run: bool):
        q = {
            "bool": {
                "filter": [
                    {"term": {"events.is_standard": "true"}},
                    {"term": {"events.metadata.data_proxy": "amazon_alexa"}},
                ]
            }
        }
        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.events[0].is_standard = false;
                """,
                "lang": "painless",
            },
        }
        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateEmptyNutritionEntries:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [{"terms": {"events.type": dt_types}}],
                "must_not": [{"exists": {"field": "events.consumables_extension"}}],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    def map = new HashMap();
                    map.put("carbohydrates",       null);
                    map.put("quantity_per_serving",null);
                    map.put("amount",              null);
                    map.put("fiber",               null);
                    map.put("quantity",            1);
                    map.put("potassium",           null);
                    map.put("calcium",             null);
                    map.put("vitamin_a",           null);
                    map.put("vitamin_c",           null);
                    map.put("units",               null);
                    map.put("calories",            null);
                    map.put("saturated_fat",       null);
                    map.put("sodium",              null);
                    map.put("monounsaturated_fat", null);
                    map.put("polyunsaturated_fat", null);
                    map.put("quantity_type",       "serving");
                    map.put("protein",             null);
                    map.put("name",                null);
                    map.put("fat",                 null);
                    map.put("trans_fat",           null);
                    map.put("cholesterol",         null);
                    map.put("iron",                null);
                    map.put("sugar",               null);
                    ctx._source.events[0].consumables_extension = map;
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateMissingUnits:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"type.events": dt_types}},
                    {"exists": {"field": "events.consumables_extension.amount"}},
                ],
                "must_not": [{"exists": {"field": "events.consumables_extension.units"}}],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.events[0].consumables_extension.quantity = ctx._source.events[0].consumables_extension.amount;
                    ctx._source.events[0].consumables_extension.amount = null;
                    ctx._source.events[0].consumables_extension.quantity_type = "serving";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateMissingQuantityType:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"events.type": dt_types}},
                ],
                "must_not": [{"exists": {"field": "events.consumables_extension.quantity_type"}}],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.events[0].consumables_extension.amount = null;
                    ctx._source.events[0].consumables_extension.quantity = 1;
                    ctx._source.events[0].consumables_extension.quantity_type = "serving";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateClearExtraUnits:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"events.type": dt_types}},
                    {"exists": {"field": "events.consumables_extension.units"}},
                ],
                "must_not": [{"exists": {"field": "events.consumables_extension.amount"}}],
            }
        }
        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.events[0].consumables_extension.units = null;
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateClearExtraQuantityType:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"events.type": dt_types}},
                    {"term": {"events.consumables_extension.quantity_type": "unit"}},
                ],
                "must_not": [{"exists": {"field": "events.consumables_extension.units"}}],
            }
        }
        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.events[0].consumables_extension.quantity_type = "serving";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateMedicationMissingQuantityTypeHasAmountUnits:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"type": dt_types}},
                ],
                "must": [
                    {"exists": {"field": "consumables_extension.amount"}},
                    {"exists": {"field": "consumables_extension.units"}},
                ],
                "must_not": [
                    {"exists": {"field": "consumables_extension.quantity_type"}},
                ],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.events[0].consumables_extension.quantity = ctx._source.consumables_extension.amount;
                    ctx._source.events[0].consumables_extension.quantity_type = "unit";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


async def run_migration(data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool = True):
    client = bootstrapper.get(interface=AsyncOpenSearch)

    await MigrateVoiceEntries().migrate(client=client, data_type=data_type, dry_run=dry_run)
    await MigrateEmptyNutritionEntries().migrate(client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types)
    await MigrateMissingUnits().migrate(client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types)
    await MigrateMissingQuantityType().migrate(client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types)
    await MigrateClearExtraUnits().migrate(client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types)
    await MigrateClearExtraQuantityType().migrate(
        client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types
    )


dt_types = [DiaryEventType.MEDICATION]

if __name__ == "__main__":
    asyncio.run(run_migration(data_type=DataType.TempPlan, dt_types=dt_types))
