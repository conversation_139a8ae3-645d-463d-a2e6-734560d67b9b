import asyncio
import logging
from typing import Sequence
from uuid import UUID

from opensearchpy import Async<PERSON>penSearch

from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.repository.temp_plan_repository import TempPlanRepository
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.query.leaf_query import ExistsQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.migrations.migration_wrapper import OSMigrationWrapper
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataTypeToIndexModelMapping
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async

logger = logging.getLogger()
logger.setLevel(logging.INFO)


class MigrateVoiceEntries:
    async def migrate(self, client: AsyncOpenSearch, data_type: DataType, dry_run: bool):
        q = {"bool": {"filter": [{"term": {"is_standard": "true"}}, {"term": {"metadata.data_proxy": "amazon_alexa"}}]}}
        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.is_standard = false;
                """,
                "lang": "painless",
            },
        }
        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateMissingPlans:
    async def migrate(
        self,
        client: AsyncOpenSearch,
        search_service: DocumentSearchService,
        agg_service: AggregationService,
        data_type: DataType,
        temp_plan_repo: TempPlanRepository,
        dry_run: bool,
    ):
        async def find_corrupted_plan_ids() -> set[UUID]:
            aggs = await agg_service.frequency_distribution_by_query(
                query=Query(
                    type_queries=[TypeQuery(domain_types=[DiaryEvents], query=ExistsQuery(field_name="plan_extension"))]
                ),
                field_name="plan_extension.plan_id",
                size=100_000,
            )
            unique_plan_ids = {UUID(a.aggregation_key) for a in aggs}  # pyright: ignore
            found_plans = await temp_plan_repo.search_by_id(ids=list(unique_plan_ids))
            found_plan_ids = {p.id for p in found_plans}

            return unique_plan_ids - found_plan_ids

        corrupted_plan_ids = await find_corrupted_plan_ids()
        if corrupted_plan_ids:
            q = ValuesQuery(field_name="plan_extension.plan_id", values=[str(p) for p in corrupted_plan_ids])
            count = await search_service.count_by_query(
                query=Query(type_queries=[TypeQuery(domain_types=[DiaryEvents], query=q)])
            )
        else:
            count = 0

        print(f"Number of entries to be migrated via {self.__class__.__name__}: at least {count}")
        if dry_run:
            return

        while corrupted_plan_ids:
            q = {"bool": {"filter": [{"terms": {"plan_extension.plan_id": [str(p) for p in corrupted_plan_ids]}}]}}
            query = {
                "query": q,
                "script": {
                    "source": """
                        ctx._source.plan_extension = null;
                    """,
                    "lang": "painless",
                },
            }

            await update_by_query_async(client=client, data_type=data_type, query=query)
            corrupted_plan_ids = await find_corrupted_plan_ids()


class MigrateMissingConsumableExtensionEntries:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [{"terms": {"type": dt_types}}],
                "must_not": [{"exists": {"field": "consumables_extension"}}],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    def map = new HashMap();
                    map.put("carbohydrates",       null);
                    map.put("quantity_per_serving",null);
                    map.put("amount",              null);
                    map.put("fiber",               null);
                    map.put("quantity",            1);
                    map.put("potassium",           null);
                    map.put("calcium",             null);
                    map.put("vitamin_a",           null);
                    map.put("vitamin_c",           null);
                    map.put("units",               null);
                    map.put("calories",            null);
                    map.put("saturated_fat",       null);
                    map.put("sodium",              null);
                    map.put("monounsaturated_fat", null);
                    map.put("polyunsaturated_fat", null);
                    map.put("quantity_type",       "serving");
                    map.put("protein",             null);
                    map.put("name",                null);
                    map.put("fat",                 null);
                    map.put("trans_fat",           null);
                    map.put("cholesterol",         null);
                    map.put("iron",                null);
                    map.put("sugar",               null);
                    ctx._source.consumables_extension = map;
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateMissingUnits:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"type": dt_types}},
                    {"exists": {"field": "consumables_extension.amount"}},
                ],
                "must_not": [{"exists": {"field": "consumables_extension.units"}}],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.consumables_extension.quantity = ctx._source.consumables_extension.amount;
                    ctx._source.consumables_extension.amount = null;
                    ctx._source.consumables_extension.quantity_type = "serving";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateMissingQuantityType:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"type": dt_types}},
                ],
                "must_not": [{"exists": {"field": "consumables_extension.quantity_type"}}],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.consumables_extension.amount = null;
                    ctx._source.consumables_extension.quantity = 1;
                    ctx._source.consumables_extension.quantity_type = "serving";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateClearExtraUnits:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"type": dt_types}},
                    {"exists": {"field": "consumables_extension.units"}},
                ],
                "must_not": [{"exists": {"field": "consumables_extension.amount"}}],
            }
        }
        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.consumables_extension.units = null;
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateClearExtraQuantityType:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"type": dt_types}},
                    {"term": {"consumables_extension.quantity_type": "unit"}},
                ],
                "must_not": [{"exists": {"field": "consumables_extension.units"}}],
            }
        }
        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.consumables_extension.quantity_type = "serving";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


class MigrateMedicationMissingQuantityTypeHasAmountUnits:
    async def migrate(
        self, client: AsyncOpenSearch, data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool
    ):
        q = {
            "bool": {
                "filter": [
                    {"terms": {"type": dt_types}},
                ],
                "must": [
                    {"exists": {"field": "consumables_extension.amount"}},
                    {"exists": {"field": "consumables_extension.units"}},
                ],
                "must_not": [
                    {"exists": {"field": "consumables_extension.quantity_type"}},
                ],
            }
        }

        index_name = DataTypeToIndexModelMapping[data_type].name
        res = await client.count(index=index_name + "*", body={"query": q})
        print(f"Number of entries to be migrated via {self.__class__.__name__}: {res["count"]}")

        if dry_run:
            return

        query = {
            "query": q,
            "script": {
                "source": """
                    ctx._source.consumables_extension.quantity = ctx._source.consumables_extension.amount;
                    ctx._source.consumables_extension.quantity_type = "unit";
                """,
                "lang": "painless",
            },
        }

        await update_by_query_async(client=client, data_type=data_type, query=query)


async def run_migration(data_type: DataType, dt_types: Sequence[DiaryEventType], dry_run: bool = True):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    agg_service = bootstrapper.get(interface=AggregationService)
    search_service = bootstrapper.get(interface=DocumentSearchService)
    temp_plan_repo = bootstrapper.get(interface=TempPlanRepository)
    if not dry_run:
        await OSMigrationWrapper.update_default_pipeline(
            data_type=data_type,
            client=client,
        )

    await MigrateVoiceEntries().migrate(client=client, data_type=data_type, dry_run=dry_run)
    await MigrateMissingConsumableExtensionEntries().migrate(
        client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types
    )
    await MigrateMissingUnits().migrate(client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types)
    await MigrateMissingQuantityType().migrate(client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types)
    await MigrateClearExtraUnits().migrate(client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types)
    await MigrateClearExtraQuantityType().migrate(
        client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types
    )
    await MigrateMedicationMissingQuantityTypeHasAmountUnits().migrate(
        client=client, data_type=data_type, dry_run=dry_run, dt_types=dt_types
    )
    await MigrateMissingPlans().migrate(
        client=client,
        agg_service=agg_service,
        search_service=search_service,
        data_type=data_type,
        temp_plan_repo=temp_plan_repo,
        dry_run=dry_run,
    )

    if not dry_run:
        index_model = DataTypeToIndexModelMapping[data_type]
        await OSMigrationWrapper.update_default_pipeline(
            data_type=data_type,
            client=client,
            default_pipeline=index_model.settings["default_pipeline"],
        )


TARGET_DATA_TYPES = [DataType.DiaryEvents]
dt_types = [DiaryEventType.MEDICATION]


if __name__ == "__main__":
    asyncio.run(run_migration(data_type=DataType.DiaryEvents, dt_types=dt_types))
