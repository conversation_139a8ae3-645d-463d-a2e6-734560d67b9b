from typing import Any, Dict

from opensearchpy import Integer, Keyword

from services.base.domain.schemas.events.person import PersonFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    PERSON_INDEX,
    OpenSearchIndex,
)


def get_person_mapping() -> Dict[str, Any]:
    person_mapping = {
        PersonFields.RATING: Integer(),
        PersonFields.CONTACT_ID: Keyword(),
    }
    return convert_dsl_mapping_to_dict(person_mapping | get_base_event_mapping(), strict_mapping=True)


def get_person_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": PERSON_INDEX,
    }


PersonIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=PERSON_INDEX,
    mappings=get_person_mapping(),
    settings=get_person_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[PERSON_INDEX],
)
