from typing import Any, Dict

from opensearchpy import Date, Keyword, Object, Text

from services.base.domain.constants.document_labels import Document<PERSON>abels
from services.base.domain.schemas.contact import ContactFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_document_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    CONTACT_INDEX,
    OpenSearchIndex,
)


def get_contact_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            ContactFields.LAST_NAME: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.FIRST_NAME: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.NICKNAME: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.COMPANY: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.STREET: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.ADDRESS: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.CITY: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.STATE: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.ZIP: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.RELATIONSHIP: Text(fields={"keyword": Keyword(ignore_above=128)}, copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.NOTE: Text(copy_to=OS_LABEL_CATCH_ALL),
            ContactFields.BIRTHDAY: Date(),
            DocumentLabels.ARCHIVED_AT: Date(),
            DocumentLabels.RBAC: Object(
                properties={
                    DocumentLabels.OWNER_ID: Keyword(),
                }
            ),
            DocumentLabels.TAGS: Object(
                properties={
                    DocumentLabels.TAG: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL)
                }
            ),
        }
        | get_document_mapping()
    )


def get_contact_settings():
    return {
        "default_pipeline": None,
        **depr_get_default_index_settings(),
    }


ContactIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=CONTACT_INDEX,
    mappings=get_contact_mapping(),
    settings=get_contact_settings(),
    is_splittable=False,
)
