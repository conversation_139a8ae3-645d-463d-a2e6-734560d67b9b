import json
import logging
from datetime import datetime, timezone
from typing import Optional, Sequence
from uuid import UUID

from opensearchpy import Async<PERSON>penSearch, OpenSearchException
from pydantic import ValidationError

from services.base.application.boundaries.documents import SearchDocumentsOutputBoundary
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.sorts import Sort
from services.base.application.exceptions import IncorrectOperationException
from services.base.application.retry import retry
from services.base.application.utils.serializers import serialize_with_datetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.extension_output import ExtensionRun
from services.base.domain.schemas.query.query import Query
from services.base.infrastructure.database.opensearch.continuation_token_utils import ContinuationTokenUtils
from services.base.infrastructure.database.opensearch.opensearch_index_constants import EXTENSION_INDEX
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataSchemaToIndexModelMapping
from services.base.infrastructure.database.opensearch.opensearch_request_builder import OpenSearchRequestBuilder
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator
from services.base.infrastructure.database.opensearch.repository.os_response_parser import OSResponseParser


class OSExtensionRunRepository(ExtensionRunRepository):
    _os_client: AsyncOpenSearch

    def __init__(self, client: AsyncOpenSearch):
        self._os_client = client

    async def insert(
        self, extension_runs: Sequence[ExtensionRun], force_strong_consistency: bool = True
    ) -> Sequence[ExtensionRun]:
        insert_requests: list[dict] = []
        for extension_run in extension_runs:
            action = {
                BulkOperation.Create.value: {
                    "_index": EXTENSION_INDEX,
                    "_id": str(extension_run.id),
                }
            }
            insert_requests.append(action)
            document = extension_run.model_dump(exclude={DocumentLabels.ID})
            document["join_field"] = {"name": extension_run.type}
            insert_requests.append(document)
        refresh = "wait_for" if force_strong_consistency else "false"
        bulk_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
        # TODO: what if response contains errors?
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response,
            action=BulkOperation.Create,
        )

        return await self.search_by_id(ids=ids)

    @retry(exceptions=OpenSearchException)
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[ExtensionRun]:
        query = {"bool": {"filter": [{"terms": {"_id": [str(doc_id) for doc_id in ids]}}]}}
        request_body = {"query": query, "size": len(ids)}
        response = await self._os_client.search(index=EXTENSION_INDEX + "*", body=request_body)
        return self._to_domain_from_search(response=response)

    @retry(exceptions=OpenSearchException)
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        delete_requests: list[dict] = []
        index_model = DataSchemaToIndexModelMapping[ExtensionRun]
        for run_id in ids:
            document = await self.search_by_id(ids=[run_id])
            index_name = OpenSearchUtils.get_index_name(
                entry=document[0], index_name=index_model.name, is_splittable=index_model.is_splittable
            )
            request = {BulkOperation.Delete.value: {"_index": index_name, "_id": str(run_id)}}

            delete_requests.append(request)

        delete_result = await self._os_client.bulk(body=delete_requests)
        return await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=delete_result, action=BulkOperation.Delete
        )

    @retry(exceptions=OpenSearchException)
    async def update(
        self, extension_runs: Sequence[ExtensionRun], force_strong_consistency: bool = True
    ) -> Sequence[ExtensionRun]:
        if not extension_runs:
            return []
        update_requests: list[dict] = []
        index_model = DataSchemaToIndexModelMapping[ExtensionRun]
        for run in extension_runs:
            run.system_properties.updated_at = datetime.now(timezone.utc)
            document = await self.search_by_id(ids=[run.id])
            index_name = OpenSearchUtils.get_index_name(
                entry=document[0], index_name=index_model.name, is_splittable=index_model.is_splittable
            )
            if run.metadata.user_uuid == document[0].metadata.user_uuid:
                action = {BulkOperation.Update.value: {"_index": index_name, "_id": str(run.id)}}
                request = {
                    "doc": {
                        **run.model_dump(
                            by_alias=True,
                            exclude={
                                DocumentLabels.ID: True,
                                DocumentLabels.USER_UUID: True,
                                DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.CREATED_AT},
                            },
                        )
                    }
                }

                update_requests.append(action)
                update_requests.append(request)

        refresh = "wait_for" if force_strong_consistency else "false"
        bulk_response = await self._os_client.bulk(body=update_requests, refresh=refresh)  # pyright: ignore
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Update
        )
        return await self.search_by_id(ids=ids)

    @retry(exceptions=OpenSearchException)
    async def search_by_query(
        self,
        query: Query,
        size: int = 10000,
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchDocumentsOutputBoundary[ExtensionRun]:
        query_as_a_dict = QueryTranslator.translate(query=query).query_as_dict

        request_builder = OpenSearchRequestBuilder().with_query_v2(query=query_as_a_dict).with_size(size=size)

        if continuation_token:
            if not sorts:
                raise ValueError("Continuation token and sort are mutually inclusive")
            request_builder.with_search_after(
                search_after=ContinuationTokenUtils.decode_continuation_token(continuation_token=continuation_token)
            )
        if sorts:
            request_builder.with_sorts(sorts=sorts)

        request_body = request_builder.build()
        logging.info(f"Searching runs with query {json.dumps(request_body, default=serialize_with_datetime)}")
        response = await self._os_client.search(
            body=request_body,
            index=EXTENSION_INDEX + "*",
        )
        return OSResponseParser._get_models_from_search_response(data_schema=ExtensionRun, response=response)

    def _to_domain_from_search(self, response: dict) -> Sequence[ExtensionRun]:
        hits = response["hits"]["hits"]
        invalid_ids = []
        extension_runs = []

        for hit in hits:
            try:
                source = hit["_source"]
                extension_runs.append(ExtensionRun(**source, id=hit["_id"]))
            except ValidationError:
                invalid_ids.append(hit["_id"])

        if invalid_ids:
            raise IncorrectOperationException(f"Part of response is not ExtensionRuns. Invalid IDs: {invalid_ids}")

        return extension_runs
