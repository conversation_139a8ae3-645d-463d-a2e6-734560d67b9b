import json
from typing import Sequence
from uuid import UUID

from opensearchpy import Async<PERSON><PERSON>Search, OpenSearchException

from services.base.application.boundaries.documents import ExtensionResultOutputBoundary
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.retry import retry
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
from services.base.infrastructure.database.opensearch.opensearch_index_constants import EXTENSION_INDEX
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataSchemaToIndexModelMapping
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils
from services.base.infrastructure.database.opensearch.repository.os_response_parser import OSResponseParser


class OSExtensionResultRepository(ExtensionResultRepository):
    _os_client: AsyncOpenSearch
    _extension_run_repository: ExtensionRunRepository

    def __init__(self, client: AsyncOpenSearch, extension_run_repository: ExtensionRunRepository):
        self._os_client = client
        self._extension_run_repository = extension_run_repository

    async def insert(
        self,
        extension_results: Sequence[ExtensionResult],
        parent_id: UUID,
        force_strong_consistency: bool = True,
    ) -> Sequence[ExtensionResult]:
        insert_requests: list[dict] = []

        for extension_result in extension_results:
            action = {
                BulkOperation.Create.value: {
                    "_index": EXTENSION_INDEX,
                    "_id": str(extension_result.id),
                    "routing": str(parent_id),
                }
            }
            insert_requests.append(action)
            document = extension_result.model_dump(exclude={DocumentLabels.ID})
            document["join_field"] = {"name": extension_result.type, "parent": str(parent_id)}
            insert_requests.append(document)
        refresh = "wait_for" if force_strong_consistency else "false"
        bulk_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
        # TODO: what if response contains errors?
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response,
            action=BulkOperation.Create,
        )

        return await self.search_by_id(ids=ids)

    @retry(exceptions=OpenSearchException)
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[ExtensionResult]:
        query = {"bool": {"filter": [{"terms": {"_id": [str(doc_id) for doc_id in ids]}}]}}
        request_body = {"query": query, "size": len(ids)}
        response = await self._os_client.search(index=EXTENSION_INDEX + "*", body=request_body)
        return await OSResponseParser.to_domain_from_search(data_schema=ExtensionResult, response=response)

    @retry(exceptions=OpenSearchException)
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        delete_requests: list[dict] = []
        index_model = DataSchemaToIndexModelMapping[ExtensionResult]
        for extension_result_id in ids:
            document = await self.search_by_id(ids=[extension_result_id])
            index_name = OpenSearchUtils.get_index_name(
                entry=document[0], index_name=index_model.name, is_splittable=index_model.is_splittable
            )
            request = {BulkOperation.Delete.value: {"_index": index_name, "_id": str(extension_result_id)}}

            delete_requests.append(request)

        delete_result = await self._os_client.bulk(body=delete_requests)
        return await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=delete_result, action=BulkOperation.Delete
        )

    @retry(exceptions=OpenSearchException)
    async def get_all_children(self, parent_id: UUID, size: int = 1000) -> Sequence[ExtensionResultOutputBoundary]:
        index_model = DataSchemaToIndexModelMapping[ExtensionRun]
        parent = (await self._extension_run_repository.search_by_id(ids=[parent_id]))[0]
        index_name = OpenSearchUtils.get_index_name(
            entry=parent, index_name=index_model.name, is_splittable=index_model.is_splittable
        )

        query = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "has_parent": {
                                "parent_type": "run",  # TODO migrate to ExtensionRun
                                "query": {"bool": {"filter": [{"term": {"_id": str(parent.id)}}]}},
                            }
                        },
                        {"term": {"type": "result"}},  # TODO migrate to ExtensionResult
                    ]
                }
            },
            "size": size,
        }

        response = await self._os_client.search(index=index_name, body=query)

        documents = []
        for hit in response["hits"]["hits"]:
            document_data = hit["_source"]
            document_data["output"] = json.loads(document_data["output"])
            document = ExtensionResultOutputBoundary(**document_data, id=hit["_id"])
            documents.append(document)

        return documents
