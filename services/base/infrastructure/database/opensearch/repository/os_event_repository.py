import asyncio
import logging
from datetime import datetime, timezone
from typing import Optional, Sequence
from uuid import UUID

from opensearchpy import Async<PERSON><PERSON>Search, OpenSearchException
from pydantic import ValidationError

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.sorts import CommonSorts, Sort
from services.base.application.exceptions import (
    IncorrectOperationException,
    RuntimeException,
)
from services.base.application.retry import retry
from services.base.application.utils.size_splitter import SizeSplitter
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.infrastructure.database.opensearch.doc_id_refiner import DocId<PERSON><PERSON>iner
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataSchemaToIndexModelMapping


class OSEventRepository(EventRepository):

    def __init__(self, client: AsyncOpenSearch, search_service: DocumentSearchService):
        self._os_client = client
        self._search_service: DocumentSearchService = search_service

    async def insert(self, events: Sequence[Event], force_strong_consistency: bool = False) -> Sequence[Event]:
        unique_types = {type(e) for e in events}

        tasks = (self._get_current_index(domain_type=event_type) for event_type in unique_types)
        type_to_index_map: dict[type[Event], str] = {
            event_type: index for event_type, index in await asyncio.gather(*tasks)
        }

        events = DocIdRefiner.refine_event_ids(events=events, type_to_index_map=type_to_index_map)
        result: list[Event] = []
        # split by size
        chunked_events = SizeSplitter.split(data=events)
        refresh = "wait_for" if force_strong_consistency else "false"

        for chunk in chunked_events:
            insert_requests: list[dict] = []
            for event in chunk:
                action = {BulkOperation.Create.value: {"_index": type_to_index_map[type(event)], "_id": str(event.id)}}
                insert_requests.append(action)
                insert_requests.append(
                    event.model_dump(exclude={DocumentLabels.ID})
                    # Serialize tags as list of dictionaries for search purposes
                    | {DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in event.tags]}
                )

                # TODO: what if response contains errors?

            os_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
            if os_response["errors"]:
                logging.error(f"There were errors during insertion of events. Response: {os_response}")
            docs = await self._do_search_by_id(
                ids_and_indices=[
                    (
                        UUID(item["create"]["_id"]),
                        item["create"]["_index"],
                    )
                    for item in os_response["items"]
                ]
            )
            result.extend(docs)

        return result

    async def update(self, events: Sequence[Event]) -> Sequence[Event]:
        result: list[Event] = []
        # split by size
        chunked_events = SizeSplitter.split(data=events)

        for chunk in chunked_events:
            update_requests: list[dict] = []
            for event in chunk:
                event.system_properties.updated_at = datetime.now(timezone.utc)
                action = {
                    BulkOperation.Update.value: {
                        "_index": self._get_index_from_id_and_type(
                            doc_id=event.id, domain_type=type(event)  # pyright: ignore
                        ),
                        "_id": str(event.id),
                    }
                }
                request = {
                    "doc": {
                        **event.model_dump(
                            by_alias=True,
                            exclude={
                                DocumentLabels.ID: True,
                                DocumentLabels.RBAC: {DocumentLabels.OWNER_ID},
                                DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.CREATED_AT},
                            },
                        )
                        # Serialize tags as list of dictionaries for search purposes
                        | {DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in event.tags]}
                    }
                }

                update_requests.append(action)
                update_requests.append(request)

            # TODO: what if response contains errors?
            os_response = await self._os_client.bulk(body=update_requests)
            if os_response["errors"]:
                logging.error(f"There were errors during the update of events. Response: {os_response}")
            result.extend(
                await self._do_search_by_id(
                    ids_and_indices=[
                        (
                            UUID(item["update"]["_id"]),
                            item["update"]["_index"],
                        )
                        for item in os_response["items"]
                    ]
                )
            )
        return result

    @retry(exceptions=OpenSearchException)
    async def search_by_id(self, ids_and_types: Sequence[tuple[UUID, type[Event]]]) -> Sequence[Event]:
        return await self._do_search_by_id(
            ids_and_indices=[
                (
                    doc_id,
                    self._get_index_from_id_and_type(doc_id=doc_id, domain_type=doc_type),
                )
                for doc_id, doc_type in ids_and_types
            ]
        )

    async def _do_search_by_id(self, ids_and_indices: Sequence[tuple[UUID, str]]) -> Sequence[Event]:
        response = await self._os_client.mget(
            body={"docs": [{"_id": doc_id, "_index": index} for doc_id, index in ids_and_indices]}
        )
        return await self._to_domain_from_mget_dynamic(response=response)

    @retry(exceptions=OpenSearchException)
    async def delete_by_id(self, ids_and_types: Sequence[tuple[UUID, type[Event]]]) -> Sequence[UUID]:
        os_response = await self._os_client.bulk(
            body=[
                {
                    BulkOperation.Delete.value: {
                        "_id": str(doc_id),
                        "_index": self._get_index_from_id_and_type(doc_id=doc_id, domain_type=doc_type),
                    }
                }
                for doc_id, doc_type in ids_and_types
            ]
        )
        if os_response["errors"]:
            logging.error(f"There were errors during deletion of events. Response: {os_response}")
        return [UUID(item["delete"]["_id"]) for item in os_response["items"]]

    async def search_by_single_query[T: Event](
        self,
        size: int,
        query: SingleDocumentTypeQuery[T],
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[T]:
        sorts = sorts if sorts else CommonSorts.created_at_and_internal_id()
        return await self._search_service.search_documents_by_single_query(
            query=query, size=size, continuation_token=continuation_token, sorts=sorts
        )

    @staticmethod
    def _get_index_from_id_and_type(doc_id: UUID, domain_type: type[Event]):
        current_index = int(doc_id.hex[0:2], 16)
        return f"{DataSchemaToIndexModelMapping[domain_type].name}-{current_index:06d}"

    @staticmethod
    async def _to_domain_from_mget_dynamic(response: dict) -> Sequence[Event]:
        result: list = []
        not_found_ids = []
        for doc in response["docs"]:
            if err := doc.get("error"):
                if err["type"] == "index_not_found_exception":
                    not_found_ids.append(doc["_id"])
                    continue
                logging.error(f"Mget search error: {err["reason"]}, id: {doc["_id"]}")
                raise RuntimeException(message=f"Error while searching document {doc["_id"]}")
            if doc["found"]:
                source = doc["_source"]
                tags = source[DocumentLabels.TAGS]
                try:
                    model = DataType(source["type"]).to_domain_model()(
                        **source | {DocumentLabels.TAGS: [tag[DocumentLabels.TAG] for tag in tags]}, id=doc["_id"]
                    )
                    result.append(model)
                except ValidationError as err:
                    logging.error(
                        "failed to deserialize event",
                        extra={
                            "document_id": doc["_id"],
                            "index": doc["_index"],
                            "source": source,
                            "error": str(err),
                        },
                    )

        if not_found_ids:
            raise IncorrectOperationException(message=f"Documents not found: {not_found_ids}")

        return result

    async def _get_current_index(self, domain_type: type[Event]) -> tuple[type[Event], str]:
        alias_name = DataSchemaToIndexModelMapping[domain_type].name
        alias_info: dict = await self._os_client.indices.get_alias(name=alias_name)

        sorted_indices = dict(sorted(alias_info.items(), key=lambda i: int(i[0].split(sep="-")[1]), reverse=True))
        for index, alias_data in sorted_indices.items():
            if alias_name in alias_data["aliases"]:
                if alias_data["aliases"][alias_name]["is_write_index"]:
                    return domain_type, index
        raise ShouldNotReachHereException(
            f"No write index found for alias {alias_name}, domain_type: {domain_type.__name__}"
        )
