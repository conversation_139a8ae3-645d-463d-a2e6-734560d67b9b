from typing import Sequence

from services.base.infrastructure.database.opensearch.index_settings.air_quality import AirQualityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.body_metric import BodyMetricIndexModel
from services.base.infrastructure.database.opensearch.index_settings.contact import ContactIndexModel
from services.base.infrastructure.database.opensearch.index_settings.core_event import CoreEventIndexModel
from services.base.infrastructure.database.opensearch.index_settings.diary_events import (
    DiaryEventsIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.event_group import EventGroupIndexModel
from services.base.infrastructure.database.opensearch.index_settings.exercise import ExerciseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.extension import (
    ExtensionIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.feeling import FeelingIndexModel
from services.base.infrastructure.database.opensearch.index_settings.heart_rate import (
    HeartRateIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.inbox_message_index import InboxMessageIndexModel
from services.base.infrastructure.database.opensearch.index_settings.location import (
    LocationIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.medication import MedicationIndexModel
from services.base.infrastructure.database.opensearch.index_settings.note import NoteIndexModel
from services.base.infrastructure.database.opensearch.index_settings.nutrition import NutritionIndexModel
from services.base.infrastructure.database.opensearch.index_settings.plan import PlanIndexModel
from services.base.infrastructure.database.opensearch.index_settings.pollen import PollenIndexModel
from services.base.infrastructure.database.opensearch.index_settings.records.sleep_record import SleepRecordIndexModel
from services.base.infrastructure.database.opensearch.index_settings.resting_heart_rate import (
    RestingHeartRateIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.shopping_activity import (
    ShoppingActivityIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.sleep import (
    SleepIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.sleep_v3 import SleepV3IndexModel
from services.base.infrastructure.database.opensearch.index_settings.steps import (
    StepsIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.symptom import (
    SymptomIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.temp_plan import TempPlanIndexModel
from services.base.infrastructure.database.opensearch.index_settings.template import TemplateIndexModel
from services.base.infrastructure.database.opensearch.index_settings.usage_statistics_output import (
    UsageStatisticsIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.use_case import UseCaseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.user_action_log import (
    UserLogsIndexModel,
)
from services.base.infrastructure.database.opensearch.index_settings.weather import WeatherIndexModel
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    OpenSearchIndex,
)
from services.base.infrastructure.database.opensearch.opensearch_mappings import ContentIndexModel


def get_events_index_models() -> Sequence[OpenSearchIndex]:
    return (
        BodyMetricIndexModel,
        ContentIndexModel,
        CoreEventIndexModel,
        ExerciseIndexModel,
        FeelingIndexModel,
        NoteIndexModel,
        NutritionIndexModel,
        SymptomIndexModel,
        EventGroupIndexModel,
        MedicationIndexModel,
        SleepV3IndexModel,
    )


def get_records_index_models() -> Sequence[OpenSearchIndex]:
    return (SleepRecordIndexModel,)


def get_os_index_models() -> Sequence[OpenSearchIndex]:
    return (
        # V3
        *get_events_index_models(),
        *get_records_index_models(),
        # Events V2
        ExtensionIndexModel,
        DiaryEventsIndexModel,
        HeartRateIndexModel,
        LocationIndexModel,
        RestingHeartRateIndexModel,
        ShoppingActivityIndexModel,
        SleepIndexModel,
        StepsIndexModel,
        # Other
        InboxMessageIndexModel,
        TempPlanIndexModel,
        UsageStatisticsIndexModel,
        UserLogsIndexModel,
        TemplateIndexModel,
        PlanIndexModel,
        ContactIndexModel,
        UseCaseIndexModel,
        # Environment
        AirQualityIndexModel,
        PollenIndexModel,
        WeatherIndexModel,
    )
