from dataclasses import dataclass
from datetime import date
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.events.document_base import (
    Document,
    RBACDocument,
    SystemPropertiesDocument,
    TagsDocument,
)
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class ContactFields:
    LAST_NAME = "last_name"
    FIRST_NAME = "first_name"
    NICKNAME = "nickname"
    COMPANY = "company"
    STREET = "street"
    ADDRESS = "address"
    CITY = "city"
    STATE = "state"
    ZIP = "zip"
    NOTE = "note"
    BIRTHDAY = "birthday"
    RELATIONSHIP = "relationship"


class ContactIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> str:
        return DataType.Contact


class Contact(Document, SystemPropertiesDocument, RBACDocument, TagsDocument, ContactIdentifier):
    type: Literal[DataType.Contact] = Field(alias=DocumentLabels.TYPE)
    last_name: NonEmptyStr = Field(alias=ContactFields.LAST_NAME)
    first_name: NonEmptyStr = Field(alias=ContactFields.FIRST_NAME)
    nickname: NonEmptyStr = Field(alias=ContactFields.NICKNAME)
    company: NonEmptyStr | None = Field(alias=ContactFields.COMPANY)
    street: NonEmptyStr | None = Field(alias=ContactFields.STREET)
    address: NonEmptyStr | None = Field(alias=ContactFields.ADDRESS)
    city: NonEmptyStr | None = Field(alias=ContactFields.CITY)
    state: NonEmptyStr | None = Field(alias=ContactFields.STATE)
    zip: NonEmptyStr | None = Field(alias=ContactFields.ZIP)
    note: NonEmptyStr | None = Field(alias=ContactFields.NOTE)
    birthday: date = Field(alias=ContactFields.BIRTHDAY)
    relationship: PersonRelationship = Field(alias=ContactFields.RELATIONSHIP)
    archived_at: SerializableAwareDatetime | None = Field(alias=DocumentLabels.ARCHIVED_AT)
