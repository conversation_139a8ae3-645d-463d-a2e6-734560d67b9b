from dataclasses import dataclass
from typing import Literal
from uuid import UUID

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class PersonFields(EventFields):
    RATING = "rating"
    CONTACT_ID = "contact_id"


class PersonIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> str:
        return DataType.Person


class Person(Event, PersonIdentifier):
    type: Literal[DataType.Person] = Field(alias=PersonFields.TYPE)
    category: PersonRelationship = Field(
        alias=PersonFields.CATEGORY,
    )
    rating: int | None = Field(
        alias=PersonFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    contact_id: UUID
