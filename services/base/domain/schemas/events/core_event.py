from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class CoreEventFields(EventFields):
    RATING = "rating"


class CoreEventCategory(StrEnum):
    CORE_EVENT = auto()
    IDLE_DOWNTIME = auto()
    IDLE_NAP = auto()
    IDLE_WAIT = auto()
    IDLE_OTHER = auto()
    INTIMACY_PARTNER = auto()
    INTIMACY_SELF = auto()
    INTIMACY_SPIRITUAL = auto()
    INTIMACY_OTHER = auto()
    SERVICE_BEAUTY = auto()
    SERVICE_DOCTOR = auto()
    SERVICE_EDUCATION = auto()
    SERVICE_FINANCE = auto()
    SERVICE_MENTAL_HEALTH = auto()
    SERVICE_PHYSICAL_HEALTH = auto()
    SERVICE_PROPERTY = auto()
    SERVICE_VETERINARIAN = auto()
    SERVICE_OTHER = auto()
    SOCIAL_ASSIST = auto()
    SOCIAL_GROUP = auto()
    SOCIAL_INDIVIDUAL = auto()
    SOCIAL_REMOTE = auto()
    SOCIAL_OTHER = auto()
    TASK_BABY_CARE = auto()
    TASK_CLEAN = auto()
    TASK_CREATE = auto()
    TASK_FINANCE = auto()
    TASK_GARDEN = auto()
    TASK_LAUNDRY = auto()
    TASK_LAWN_MAINTENANCE = auto()
    TASK_MEAL = auto()
    TASK_MEAL_PREPARATION = auto()
    TASK_PARENT = auto()
    TASK_PET_CARE = auto()
    TASK_PLAN = auto()
    TASK_HOME_CARE = auto()
    TASK_CAR_CARE = auto()
    TASK_STUDY = auto()
    TASK_TECHNOLOGY = auto()
    TASK_VOLUNTEER = auto()
    TASK_OTHER = auto()
    TRAVEL_BIKE = auto()
    TRAVEL_BOAT = auto()
    TRAVEL_DRIVE = auto()
    TRAVEL_FLY = auto()
    TRAVEL_WALK = auto()
    TRAVEL_OTHER = auto()
    WORK_MENTOR = auto()
    WORK_NETWORK = auto()
    WORK_PRIMARY_WORK = auto()
    WORK_PROFESSIONAL_DEVELOPMENT = auto()
    WORK_SUPPLEMENTAL_WORK = auto()
    WORK_OTHER = auto()
    RATING_POSITIVE = auto()
    RATING_NEGATIVE = auto()
    RATING_NEUTRAL = auto()


class CoreEventIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> str:
        return DataType.CoreEvent


class CoreEvent(Event, CoreEventIdentifier):
    type: Literal[DataType.CoreEvent] = Field(alias=CoreEventFields.TYPE)
    category: CoreEventCategory = Field(
        alias=CoreEventFields.CATEGORY,
    )
    rating: int | None = Field(
        alias=CoreEventFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
