from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import PersonRelationship
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.person import PersonFields, PersonIdentifier
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase


class PersonTemplatePayload(TemplatePayloadBase, PersonIdentifier):
    type: Literal[DataType.Person] = Field(alias=PersonFields.TYPE)
    category: PersonRelationship = Field(alias=PersonFields.CATEGORY)
    rating: int | None = Field(
        alias=PersonFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
