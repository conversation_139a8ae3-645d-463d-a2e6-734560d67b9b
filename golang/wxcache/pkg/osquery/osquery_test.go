package osquery

import (
	"testing"
	"time"

	"llif.org/wxcache/pkg/testutil"
)

func TestCreateQueryShouldPass(t *testing.T) {
	var (
		query         = Query(Bool().Filter(Range("field").Gt(0).Lt(10))).Map()
		expectedQuery = map[string]any{
			"query": map[string]any{
				"bool": map[string]any{
					"filter": []map[string]any{
						{
							"range": map[string]any{
								"field": map[string]any{
									"gt": 0,
									"lt": 10,
								},
							},
						},
					},
				},
			},
		}
	)

	if !testutil.EqualMap(query, expectedQuery) {
		t.Fatalf("expected query did not match real query, expected: %#v, got: %#v", expectedQuery, query)
	}
}

func TestCreateQueryWithAggregationShouldPass(t *testing.T) {
	var (
		timeNow     = time.Now().UTC().Format(time.RFC3339)
		timeADayAgo = time.Now().UTC().Add(-24 * time.Hour).Format(time.RFC3339)

		query = Query(
			Bool().Filter(Range("field").Gt(0).Lt(10)),
		).Aggs(
			DateHistogramAggregation("my_agg", "timestamp").Interval("hour").ExtendedBounds(timeADayAgo, timeNow),
		).Map()

		expectedQuery = map[string]any{
			"query": map[string]any{
				"bool": map[string]any{
					"filter": []map[string]any{
						{
							"range": map[string]any{
								"field": map[string]any{
									"gt": 0,
									"lt": 10,
								},
							},
						},
					},
				},
			},
			"aggs": map[string]any{
				"my_agg": map[string]any{
					"date_histogram": map[string]any{
						"field":    "timestamp",
						"interval": "hour",
						"extended_bounds": map[string]any{
							"min": timeADayAgo,
							"max": timeNow,
						},
					},
				},
			},
		}
	)
	if !testutil.EqualMap(query, expectedQuery) {
		t.Fatalf("expected query did not match real query, expected: %#v, got: %#v", expectedQuery, query)
	}
}
